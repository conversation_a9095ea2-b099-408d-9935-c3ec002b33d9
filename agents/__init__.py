"""
ADK Analyst Agents Package

This package contains all the specialized agents for the ADK Analyst platform:
- Jenkins Agent: CI/CD pipeline analysis
- GitHub Agent: Repository analytics
- Future agents: DORA Metrics, JFrog, etc.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "<PERSON><PERSON><PERSON>"

# Import agents for easy access
try:
    from .github.agent import github_agent
    from .github.agent import root_agent as github_root_agent
    from .jenkins.agent import jenkins_agent
    from .jenkins.agent import root_agent as jenkins_root_agent

    # Export main agents
    __all__ = [
        "jenkins_agent",
        "github_agent",
        "jenkins_root_agent",
        "github_root_agent",
    ]

except ImportError as e:
    # Handle import errors gracefully during development
    print(f"Warning: Could not import all agents: {e}")
    __all__ = []
