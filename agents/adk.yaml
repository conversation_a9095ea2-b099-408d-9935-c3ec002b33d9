# Multi-Agent ADK Configuration
# This configuration provides access to all agents through a single interface

# Agent configuration
agent:
  name: "multi_agent_router"
  display_name: "ADK Analyst Multi-Agent Platform"
  description: "Enterprise-grade multi-agent platform for Jenkins and GitHub analysis"
  
# Model configuration
model:
  name: "gemini-2.5-pro-preview-05-06"
  temperature: 0.1
  max_tokens: 8192

# Streaming configuration
streaming:
  enabled: true
  delay_ms: 50

# Environment configuration
environment:
  google_cloud_project: "truxtsaas"
  google_cloud_location: "us-central1"
  google_genai_use_vertexai: true

# Security configuration
security:
  enable_audit_logging: true
  enable_rate_limiting: true
  max_requests_per_minute: 60
  enable_input_validation: true

# Performance configuration
performance:
  enable_caching: true
  cache_ttl_seconds: 300
  max_concurrent_requests: 10

# Logging configuration
logging:
  level: "INFO"
  format: "structured"
  enable_performance_logging: true
