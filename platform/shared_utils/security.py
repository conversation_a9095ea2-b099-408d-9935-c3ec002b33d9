"""Shared security utilities for enterprise agents."""

import time
import hashlib
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Set
from collections import defaultdict, deque

from .exceptions import RateLimitError, SecurityError
from .logging import get_logger, log_security_event
from .models import RateLimitInfo

logger = get_logger(__name__)


class SecurityValidator:
    """Security validation utilities."""
    
    def __init__(self):
        self.suspicious_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'vbscript:',
            r'onload\s*=',
            r'onerror\s*=',
            r'eval\s*\(',
            r'exec\s*\(',
            r'system\s*\(',
            r'shell_exec\s*\(',
            r'passthru\s*\(',
            r'file_get_contents\s*\(',
            r'fopen\s*\(',
            r'fwrite\s*\(',
            r'include\s*\(',
            r'require\s*\(',
            r'\.\./',
            r'\.\.\\',
        ]
    
    async def validate_input_security(self, input_data: Dict[str, Any]) -> bool:
        """
        Validate input data for security threats.
        
        Args:
            input_data: Input data to validate
            
        Returns:
            bool: True if input is safe
            
        Raises:
            SecurityError: If security threat is detected
        """
        import re
        
        for key, value in input_data.items():
            if isinstance(value, str):
                # Check for suspicious patterns
                for pattern in self.suspicious_patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        await log_security_event(
                            event_type="suspicious_input_detected",
                            severity="HIGH",
                            description=f"Suspicious pattern detected in field {key}",
                            pattern=pattern,
                            field=key,
                            value=value[:100]  # Log first 100 chars only
                        )
                        raise SecurityError(
                            f"Suspicious input detected in field {key}",
                            violation_type="injection_attempt"
                        )
                
                # Check for excessively long inputs
                if len(value) > 10000:
                    await log_security_event(
                        event_type="oversized_input_detected",
                        severity="MEDIUM",
                        description=f"Oversized input in field {key}",
                        field=key,
                        length=len(value)
                    )
                    raise SecurityError(
                        f"Input too large in field {key}",
                        violation_type="oversized_input"
                    )
        
        return True
    
    def generate_request_signature(self, request_data: Dict[str, Any]) -> str:
        """
        Generate a signature for request data.
        
        Args:
            request_data: Request data to sign
            
        Returns:
            str: Request signature
        """
        # Create a deterministic string from request data
        sorted_items = sorted(request_data.items())
        data_string = str(sorted_items)
        
        # Generate SHA-256 hash
        return hashlib.sha256(data_string.encode()).hexdigest()
    
    async def detect_anomalous_behavior(
        self,
        user_id: str,
        action: str,
        resource: str
    ) -> bool:
        """
        Detect anomalous user behavior patterns.
        
        Args:
            user_id: User identifier
            action: Action being performed
            resource: Resource being accessed
            
        Returns:
            bool: True if behavior is normal, False if anomalous
        """
        # This is a simplified implementation
        # In production, this would use ML models or more sophisticated analysis
        
        # For now, just log the activity
        logger.debug(
            "Analyzing user behavior",
            extra={
                "user_id": user_id,
                "action": action,
                "resource": resource
            }
        )
        
        return True


class RateLimiter:
    """Rate limiting utilities with multiple strategies."""
    
    def __init__(self):
        self.request_counts = defaultdict(deque)
        self.blocked_users = {}
        self.global_limits = {
            "requests_per_minute": 60,
            "requests_per_hour": 1000,
            "requests_per_day": 10000
        }
        self.user_limits = {
            "requests_per_minute": 30,
            "requests_per_hour": 500,
            "requests_per_day": 5000
        }
    
    async def check_rate_limit(
        self,
        user_id: str,
        endpoint: str = "default",
        custom_limits: Optional[Dict[str, int]] = None
    ) -> RateLimitInfo:
        """
        Check if request is within rate limits.
        
        Args:
            user_id: User identifier
            endpoint: API endpoint being accessed
            custom_limits: Custom rate limits for this request
            
        Returns:
            RateLimitInfo: Rate limit information
            
        Raises:
            RateLimitError: If rate limit is exceeded
        """
        current_time = time.time()
        
        # Check if user is temporarily blocked
        if user_id in self.blocked_users:
            block_until = self.blocked_users[user_id]
            if current_time < block_until:
                raise RateLimitError(
                    "User temporarily blocked due to rate limit violations",
                    retry_after=int(block_until - current_time)
                )
            else:
                # Unblock user
                del self.blocked_users[user_id]
        
        # Use custom limits if provided, otherwise use default user limits
        limits = custom_limits or self.user_limits
        
        # Check rate limits for different time windows
        for period, limit in limits.items():
            if period == "requests_per_minute":
                window_seconds = 60
            elif period == "requests_per_hour":
                window_seconds = 3600
            elif period == "requests_per_day":
                window_seconds = 86400
            else:
                continue
            
            # Clean old requests outside the window
            user_key = f"{user_id}:{endpoint}:{period}"
            request_times = self.request_counts[user_key]
            
            while request_times and request_times[0] < current_time - window_seconds:
                request_times.popleft()
            
            # Check if limit is exceeded
            if len(request_times) >= limit:
                # Block user for escalating violations
                if len(request_times) > limit * 1.5:
                    self.blocked_users[user_id] = current_time + 300  # Block for 5 minutes
                
                await log_security_event(
                    event_type="rate_limit_exceeded",
                    severity="MEDIUM",
                    user_id=user_id,
                    endpoint=endpoint,
                    period=period,
                    limit=limit,
                    current_count=len(request_times)
                )
                
                raise RateLimitError(
                    f"Rate limit exceeded for {period}",
                    retry_after=int(window_seconds - (current_time - request_times[0]))
                )
        
        # Record this request
        for period in limits.keys():
            user_key = f"{user_id}:{endpoint}:{period}"
            self.request_counts[user_key].append(current_time)
        
        # Calculate remaining requests
        minute_key = f"{user_id}:{endpoint}:requests_per_minute"
        minute_requests = self.request_counts[minute_key]
        remaining = max(0, limits.get("requests_per_minute", 30) - len(minute_requests))
        
        # Calculate reset time
        reset_time = int(current_time + 60)  # Next minute
        
        return RateLimitInfo(
            limit=limits.get("requests_per_minute", 30),
            remaining=remaining,
            reset_time=reset_time
        )
    
    async def check_api_rate_limit(
        self,
        service_name: str,
        response_headers: Dict[str, str]
    ) -> Optional[RateLimitInfo]:
        """
        Check API rate limit from service response headers.
        
        Args:
            service_name: Name of the external service
            response_headers: HTTP response headers
            
        Returns:
            RateLimitInfo: Rate limit information if available
        """
        if service_name.lower() == "github":
            return self._parse_github_rate_limit(response_headers)
        elif service_name.lower() == "jenkins":
            return self._parse_jenkins_rate_limit(response_headers)
        else:
            return self._parse_generic_rate_limit(response_headers)
    
    def _parse_github_rate_limit(self, headers: Dict[str, str]) -> Optional[RateLimitInfo]:
        """Parse GitHub API rate limit headers."""
        try:
            limit = int(headers.get("X-RateLimit-Limit", 0))
            remaining = int(headers.get("X-RateLimit-Remaining", 0))
            reset_time = int(headers.get("X-RateLimit-Reset", 0))
            
            if limit > 0:
                return RateLimitInfo(
                    limit=limit,
                    remaining=remaining,
                    reset_time=reset_time
                )
        except (ValueError, TypeError):
            pass
        
        return None
    
    def _parse_jenkins_rate_limit(self, headers: Dict[str, str]) -> Optional[RateLimitInfo]:
        """Parse Jenkins API rate limit headers."""
        # Jenkins doesn't typically have standard rate limit headers
        # This is a placeholder for custom implementation
        return None
    
    def _parse_generic_rate_limit(self, headers: Dict[str, str]) -> Optional[RateLimitInfo]:
        """Parse generic rate limit headers."""
        try:
            # Try common header names
            limit = None
            remaining = None
            reset_time = None
            
            for header_name, header_value in headers.items():
                header_lower = header_name.lower()
                
                if "rate-limit" in header_lower and "limit" in header_lower:
                    limit = int(header_value)
                elif "rate-limit" in header_lower and "remaining" in header_lower:
                    remaining = int(header_value)
                elif "rate-limit" in header_lower and "reset" in header_lower:
                    reset_time = int(header_value)
            
            if limit is not None and remaining is not None:
                return RateLimitInfo(
                    limit=limit,
                    remaining=remaining,
                    reset_time=reset_time or int(time.time() + 3600)
                )
        except (ValueError, TypeError):
            pass
        
        return None
