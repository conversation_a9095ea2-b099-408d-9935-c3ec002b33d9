"""Shared exception classes for enterprise agents."""

from typing import Any, Dict, Optional


class AgentError(Exception):
    """Base exception for all agent operations."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary format."""
        return {
            "code": self.error_code,
            "message": self.message,
            "details": self.details
        }


class ConnectionError(AgentError):
    """Connection to external service failed."""
    
    def __init__(
        self,
        message: str = "Failed to connect to external service",
        service_url: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if service_url:
            details["service_url"] = service_url
        super().__init__(message, details=details, **kwargs)


class AuthenticationError(AgentError):
    """Authentication failed."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        username: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if username:
            details["username"] = username
        super().__init__(message, details=details, **kwargs)


class AuthorizationError(AgentError):
    """User lacks required permissions."""
    
    def __init__(
        self,
        message: str = "Insufficient permissions",
        required_permission: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if required_permission:
            details["required_permission"] = required_permission
        super().__init__(message, details=details, **kwargs)


class RateLimitError(AgentError):
    """API rate limit exceeded."""
    
    def __init__(
        self,
        message: str = "API rate limit exceeded",
        retry_after: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if retry_after:
            details["retry_after"] = retry_after
        super().__init__(message, details=details, **kwargs)


class ValidationError(AgentError):
    """Input validation failed."""
    
    def __init__(
        self,
        message: str = "Input validation failed",
        field: Optional[str] = None,
        value: Optional[Any] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)
        super().__init__(message, details=details, **kwargs)


class UnauthorizedError(AgentError):
    """Unauthorized access attempt."""
    
    def __init__(
        self,
        message: str = "Unauthorized access",
        resource: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if resource:
            details["resource"] = resource
        super().__init__(message, details=details, **kwargs)


class SecurityError(AgentError):
    """Security violation detected."""
    
    def __init__(
        self,
        message: str = "Security violation",
        violation_type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if violation_type:
            details["violation_type"] = violation_type
        super().__init__(message, details=details, **kwargs)


class ConfigurationError(AgentError):
    """Configuration error."""
    
    def __init__(
        self, 
        message: str = "Configuration error",
        config_key: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if config_key:
            details["config_key"] = config_key
        super().__init__(message, details=details, **kwargs)


class APIError(AgentError):
    """External API returned an error."""
    
    def __init__(
        self, 
        message: str = "External API error",
        status_code: Optional[int] = None,
        response_body: Optional[str] = None,
        api_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if status_code:
            details["status_code"] = status_code
        if response_body:
            details["response_body"] = response_body
        if api_name:
            details["api_name"] = api_name
        super().__init__(message, details=details, **kwargs)


class TimeoutError(AgentError):
    """Operation timed out."""
    
    def __init__(
        self,
        message: str = "Operation timed out",
        timeout_seconds: Optional[float] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
        super().__init__(message, details=details, **kwargs)


class DataNotFoundError(AgentError):
    """Requested data not found."""
    
    def __init__(
        self,
        message: str = "Data not found",
        resource_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if resource_id:
            details["resource_id"] = resource_id
        super().__init__(message, details=details, **kwargs)


class QuotaExceededError(AgentError):
    """Service quota exceeded."""

    def __init__(
        self,
        message: str = "Service quota exceeded",
        quota_type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if quota_type:
            details["quota_type"] = quota_type
        super().__init__(message, details=details, **kwargs)


# Service-specific exceptions
class JenkinsAPIError(APIError):
    """Jenkins API specific error."""

    def __init__(
        self,
        message: str = "Jenkins API error",
        **kwargs: Any
    ) -> None:
        kwargs.setdefault("api_name", "Jenkins")
        super().__init__(message, **kwargs)


class GitHubAPIError(APIError):
    """GitHub API specific error."""

    def __init__(
        self,
        message: str = "GitHub API error",
        **kwargs: Any
    ) -> None:
        kwargs.setdefault("api_name", "GitHub")
        super().__init__(message, **kwargs)
