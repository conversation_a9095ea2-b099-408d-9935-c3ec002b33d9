"""Shared utilities for enterprise-grade agents."""

from .auth import get_credentials_from_secret_manager, validate_token_credentials
from .exceptions import (
    AgentError,
    ConnectionError,
    AuthenticationError,
    RateLimitError,
    ValidationError,
    UnauthorizedError,
    SecurityError,
    ConfigurationError,
    APIError
)
from .logging import setup_logging, get_logger, log_audit_event, log_error_event, log_security_event
from .validation import validate_request_permissions, sanitize_input, validate_url
from .models import BaseResponse, ResponseMetadata, ErrorInfo
from .security import SecurityValidator, RateLimiter
from .performance import CacheManager, PerformanceMonitor

__all__ = [
    # Authentication
    "get_credentials_from_secret_manager",
    "validate_token_credentials",
    
    # Exceptions
    "AgentError",
    "ConnectionError", 
    "AuthenticationError",
    "RateLimitError",
    "ValidationError",
    "UnauthorizedError",
    "SecurityError",
    "ConfigurationError",
    "APIError",
    
    # Logging
    "setup_logging",
    "get_logger",
    "log_audit_event",
    "log_error_event", 
    "log_security_event",
    
    # Validation
    "validate_request_permissions",
    "sanitize_input",
    "validate_url",
    
    # Models
    "BaseResponse",
    "ResponseMetadata",
    "ErrorInfo",
    
    # Security
    "SecurityValidator",
    "RateLimiter",
    
    # Performance
    "CacheManager",
    "PerformanceMonitor"
]
