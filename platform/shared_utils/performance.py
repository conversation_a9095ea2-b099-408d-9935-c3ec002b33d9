"""Shared performance utilities for enterprise agents."""

import time
import asyncio
import hashlib
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable
from collections import OrderedDict
from functools import wraps

from .logging import get_logger, log_performance_metrics
from .models import CacheEntry, PerformanceMetrics

logger = get_logger(__name__)


class CacheManager:
    """Thread-safe cache manager with TTL and LRU eviction."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = OrderedDict()
        self._lock = asyncio.Lock()
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        async with self._lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # Check if expired
            if entry.expires_at and datetime.utcnow() > entry.expires_at:
                del self.cache[key]
                return None
            
            # Update access info
            entry.access_count += 1
            entry.last_accessed = datetime.utcnow()
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            
            return entry.value
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> None:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (optional)
        """
        async with self._lock:
            ttl = ttl or self.default_ttl
            expires_at = datetime.utcnow() + timedelta(seconds=ttl) if ttl > 0 else None
            
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.utcnow(),
                expires_at=expires_at
            )
            
            self.cache[key] = entry
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            
            # Evict oldest entries if cache is full
            while len(self.cache) > self.max_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
    
    async def delete(self, key: str) -> bool:
        """
        Delete value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            bool: True if key was deleted, False if not found
        """
        async with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        async with self._lock:
            self.cache.clear()
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        async with self._lock:
            total_entries = len(self.cache)
            expired_entries = 0
            total_access_count = 0
            
            now = datetime.utcnow()
            for entry in self.cache.values():
                if entry.expires_at and now > entry.expires_at:
                    expired_entries += 1
                total_access_count += entry.access_count
            
            return {
                "total_entries": total_entries,
                "expired_entries": expired_entries,
                "max_size": self.max_size,
                "utilization_percent": round((total_entries / self.max_size) * 100, 2),
                "total_access_count": total_access_count,
                "average_access_count": round(total_access_count / total_entries, 2) if total_entries > 0 else 0
            }


class PerformanceMonitor:
    """Performance monitoring and metrics collection."""
    
    def __init__(self):
        self.metrics_history = []
        self.operation_stats = {}
    
    async def record_operation(
        self,
        operation: str,
        duration_ms: int,
        success: bool = True,
        error_type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        Record operation performance metrics.
        
        Args:
            operation: Operation name
            duration_ms: Operation duration in milliseconds
            success: Whether operation was successful
            error_type: Error type if operation failed
            **kwargs: Additional metrics data
        """
        metrics = PerformanceMetrics(
            operation=operation,
            duration_ms=duration_ms,
            timestamp=datetime.utcnow(),
            success=success,
            error_type=error_type,
            resource_usage=kwargs
        )
        
        self.metrics_history.append(metrics)
        
        # Update operation statistics
        if operation not in self.operation_stats:
            self.operation_stats[operation] = {
                "total_calls": 0,
                "successful_calls": 0,
                "failed_calls": 0,
                "total_duration_ms": 0,
                "min_duration_ms": float('inf'),
                "max_duration_ms": 0,
                "error_types": {}
            }
        
        stats = self.operation_stats[operation]
        stats["total_calls"] += 1
        stats["total_duration_ms"] += duration_ms
        stats["min_duration_ms"] = min(stats["min_duration_ms"], duration_ms)
        stats["max_duration_ms"] = max(stats["max_duration_ms"], duration_ms)
        
        if success:
            stats["successful_calls"] += 1
        else:
            stats["failed_calls"] += 1
            if error_type:
                stats["error_types"][error_type] = stats["error_types"].get(error_type, 0) + 1
        
        # Log performance metrics
        await log_performance_metrics(
            operation=operation,
            duration_ms=duration_ms,
            success=success,
            error_type=error_type,
            **kwargs
        )
        
        # Keep only recent metrics (last 1000 entries)
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
    
    async def get_operation_stats(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """
        Get performance statistics for operations.
        
        Args:
            operation: Specific operation name (optional, returns all if not provided)
            
        Returns:
            Dict containing performance statistics
        """
        if operation:
            if operation not in self.operation_stats:
                return {}
            
            stats = self.operation_stats[operation].copy()
            if stats["total_calls"] > 0:
                stats["average_duration_ms"] = round(stats["total_duration_ms"] / stats["total_calls"], 2)
                stats["success_rate_percent"] = round((stats["successful_calls"] / stats["total_calls"]) * 100, 2)
            
            return {operation: stats}
        else:
            # Return all operation stats
            result = {}
            for op_name, stats in self.operation_stats.items():
                op_stats = stats.copy()
                if op_stats["total_calls"] > 0:
                    op_stats["average_duration_ms"] = round(op_stats["total_duration_ms"] / op_stats["total_calls"], 2)
                    op_stats["success_rate_percent"] = round((op_stats["successful_calls"] / op_stats["total_calls"]) * 100, 2)
                result[op_name] = op_stats
            
            return result
    
    async def get_recent_metrics(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get recent performance metrics.
        
        Args:
            limit: Maximum number of metrics to return
            
        Returns:
            List of recent performance metrics
        """
        recent_metrics = self.metrics_history[-limit:] if self.metrics_history else []
        return [
            {
                "operation": m.operation,
                "duration_ms": m.duration_ms,
                "timestamp": m.timestamp.isoformat(),
                "success": m.success,
                "error_type": m.error_type,
                "resource_usage": m.resource_usage
            }
            for m in recent_metrics
        ]


class AsyncBatchProcessor:
    """Batch processor for efficient API calls."""
    
    def __init__(self, batch_size: int = 10, max_concurrent: int = 5):
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_batch(
        self,
        items: List[Any],
        processor_func: Callable,
        **kwargs: Any
    ) -> List[Any]:
        """
        Process items in batches with concurrency control.
        
        Args:
            items: List of items to process
            processor_func: Async function to process each item
            **kwargs: Additional arguments for processor function
            
        Returns:
            List of processed results
        """
        results = []
        
        # Split items into batches
        batches = [
            items[i:i + self.batch_size]
            for i in range(0, len(items), self.batch_size)
        ]
        
        async def process_single_batch(batch):
            async with self.semaphore:
                batch_results = []
                for item in batch:
                    try:
                        result = await processor_func(item, **kwargs)
                        batch_results.append(result)
                    except Exception as e:
                        logger.error(f"Error processing item {item}: {e}")
                        batch_results.append(None)
                return batch_results
        
        # Process batches concurrently
        batch_tasks = [process_single_batch(batch) for batch in batches]
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        # Flatten results
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                logger.error(f"Batch processing error: {batch_result}")
                continue
            results.extend(batch_result)
        
        return results


# Global instances
cache_manager = CacheManager()
performance_monitor = PerformanceMonitor()


def cached(ttl: int = 300, key_func: Optional[Callable] = None):
    """
    Decorator for caching function results.

    Args:
        ttl: Time to live in seconds
        key_func: Function to generate cache key (optional)
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = cache_manager._generate_key(func.__name__, *args, **kwargs)

            # Try to get from cache
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, ttl)

            return result

        return wrapper
    return decorator


def timed(operation_name: Optional[str] = None):
    """
    Decorator for timing function execution.

    Args:
        operation_name: Name for the operation (optional, uses function name if not provided)
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            op_name = operation_name or func.__name__
            start_time = time.time()

            try:
                result = await func(*args, **kwargs)
                success = True
                error_type = None
            except Exception as e:
                result = None
                success = False
                error_type = type(e).__name__
                raise
            finally:
                end_time = time.time()
                duration_ms = int((end_time - start_time) * 1000)

                await performance_monitor.record_operation(
                    operation=op_name,
                    duration_ms=duration_ms,
                    success=success,
                    error_type=error_type
                )

            return result

        return wrapper
    return decorator
