"""Shared logging utilities for enterprise agents."""

import logging
import sys
import time
from datetime import datetime
from typing import Any, Optional

import structlog
from google.cloud import logging as cloud_logging

from .models import AuditEvent, SecurityEvent, PerformanceMetrics


def is_production() -> bool:
    """Check if running in production environment."""
    import os
    return os.getenv("ENVIRONMENT", "development").lower() == "production"


def setup_logging(service_name: str = "agent", log_level: str = "INFO") -> None:
    """Set up structured logging with Google Cloud Logging integration."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if is_production() else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper()),
    )
    
    # Set up Google Cloud Logging in production
    if is_production():
        try:
            import os
            project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
            if project_id:
                client = cloud_logging.Client(project=project_id)
                client.setup_logging()
                
                # Create a Cloud Logging handler
                handler = client.get_default_handler()
                
                # Add the handler to the root logger
                root_logger = logging.getLogger()
                root_logger.addHandler(handler)
                
        except Exception as e:
            # Fall back to console logging if Cloud Logging setup fails
            logging.warning(f"Failed to setup Google Cloud Logging: {e}")


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        structlog.stdlib.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


async def log_audit_event(
    event_type: str,
    user: Optional[Any] = None,
    resource: Optional[str] = None,
    action: Optional[str] = None,
    result: str = "success",
    **kwargs: Any
) -> None:
    """
    Log an audit event for compliance and monitoring.
    
    Args:
        event_type: Type of audit event
        user: User identity (optional)
        resource: Resource accessed (optional)
        action: Action performed (optional)
        result: Event result (success, failure, warning)
        **kwargs: Additional event data
    """
    logger = get_logger("audit")
    
    audit_data = {
        "event_type": event_type,
        "timestamp": datetime.utcnow().isoformat(),
        "resource": resource,
        "action": action,
        "result": result,
        **kwargs
    }
    
    if user and hasattr(user, 'user_id'):
        audit_data["user_id"] = user.user_id
    
    logger.info("Audit event", **audit_data)


async def log_security_event(
    event_type: str,
    severity: str = "MEDIUM",
    user: Optional[Any] = None,
    description: str = "",
    **kwargs: Any
) -> None:
    """
    Log a security event for monitoring and alerting.
    
    Args:
        event_type: Type of security event
        severity: Event severity (LOW, MEDIUM, HIGH, CRITICAL)
        user: User identity (optional)
        description: Event description
        **kwargs: Additional event data
    """
    logger = get_logger("security")
    
    security_data = {
        "event_type": event_type,
        "severity": severity,
        "timestamp": datetime.utcnow().isoformat(),
        "description": description,
        **kwargs
    }
    
    if user and hasattr(user, 'user_id'):
        security_data["user_id"] = user.user_id
    
    logger.warning("Security event", **security_data)


async def log_error_event(
    event_type: str,
    error: str,
    user: Optional[Any] = None,
    **kwargs: Any
) -> None:
    """
    Log an error event for monitoring and debugging.
    
    Args:
        event_type: Type of error event
        error: Error message or exception
        user: User identity (optional)
        **kwargs: Additional event data
    """
    logger = get_logger("error")
    
    error_data = {
        "event_type": event_type,
        "error": str(error),
        "timestamp": datetime.utcnow().isoformat(),
        **kwargs
    }
    
    if user and hasattr(user, 'user_id'):
        error_data["user_id"] = user.user_id
    
    logger.error("Error event", **error_data)


async def log_performance_metrics(
    operation: str,
    duration_ms: int,
    success: bool = True,
    error_type: Optional[str] = None,
    **kwargs: Any
) -> None:
    """
    Log performance metrics for monitoring and optimization.

    Args:
        operation: Operation name
        duration_ms: Operation duration in milliseconds
        success: Whether operation was successful
        error_type: Error type if operation failed
        **kwargs: Additional metrics data
    """
    logger = get_logger("performance")

    metrics_data = {
        "operation": operation,
        "duration_ms": duration_ms,
        "success": success,
        "timestamp": datetime.utcnow().isoformat(),
        **kwargs
    }

    if error_type:
        metrics_data["error_type"] = error_type

    logger.info("Performance metrics", **metrics_data)


async def log_performance_event(
    operation: str,
    duration_ms: int,
    success: bool = True,
    **kwargs: Any
) -> None:
    """
    Log a performance event (alias for log_performance_metrics).

    Args:
        operation: Operation name
        duration_ms: Operation duration in milliseconds
        success: Whether operation was successful
        **kwargs: Additional event data
    """
    await log_performance_metrics(
        operation=operation,
        duration_ms=duration_ms,
        success=success,
        **kwargs
    )


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string."""
    return dt.isoformat() + "Z" if dt.tzinfo is None else dt.isoformat()


class PerformanceTimer:
    """Context manager for measuring operation performance."""
    
    def __init__(self, operation: str, logger_name: str = "performance"):
        self.operation = operation
        self.logger = get_logger(logger_name)
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration_ms = int((self.end_time - self.start_time) * 1000)
        
        success = exc_type is None
        error_type = exc_type.__name__ if exc_type else None
        
        self.logger.info(
            "Operation completed",
            operation=self.operation,
            duration_ms=duration_ms,
            success=success,
            error_type=error_type
        )


class LoggingContext:
    """Context manager for adding logging context."""
    
    def __init__(self, **context):
        self.context = context
        self.logger = None
    
    def __enter__(self):
        self.logger = structlog.get_logger().bind(**self.context)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
