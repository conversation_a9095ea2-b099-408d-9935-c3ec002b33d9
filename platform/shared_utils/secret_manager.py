"""
Google Secret Manager integration for secure credential storage.
Provides enterprise-grade credential management with caching and error handling.
"""

import os
import json
import asyncio
from typing import Dict, Any, Optional, Union
from functools import lru_cache
from google.cloud import secretmanager
from google.api_core import exceptions as gcp_exceptions
import structlog

from .logging import get_logger
from .performance import performance_monitor
from .exceptions import AuthenticationError, ConfigurationError

logger = get_logger(__name__)


class SecretManagerClient:
    """
    Enterprise-grade Google Secret Manager client with caching and error handling.
    """
    
    def __init__(self, project_id: Optional[str] = None):
        """
        Initialize Secret Manager client.
        
        Args:
            project_id: Google Cloud project ID. If None, uses GOOGLE_CLOUD_PROJECT env var.
        """
        self.project_id = project_id or os.getenv('GOOGLE_CLOUD_PROJECT')
        if not self.project_id:
            raise ConfigurationError("GOOGLE_CLOUD_PROJECT environment variable not set")
        
        self._client = None
        self._use_secret_manager = os.getenv('USE_SECRET_MANAGER', 'false').lower() == 'true'
        
        logger.info(
            "Secret Manager client initialized",
            extra={
                'project_id': self.project_id,
                'use_secret_manager': self._use_secret_manager
            }
        )
    
    @property
    def client(self) -> secretmanager.SecretManagerServiceClient:
        """Lazy initialization of Secret Manager client."""
        if self._client is None:
            try:
                self._client = secretmanager.SecretManagerServiceClient()
                logger.info("Secret Manager client created successfully")
            except Exception as e:
                logger.error(f"Failed to create Secret Manager client: {e}")
                raise AuthenticationError(f"Secret Manager client initialization failed: {e}")
        return self._client
    
    @performance_monitor
    async def get_secret(self, secret_name: str, version: str = "latest") -> str:
        """
        Retrieve a secret from Google Secret Manager.
        
        Args:
            secret_name: Name of the secret
            version: Version of the secret (default: "latest")
            
        Returns:
            Secret value as string
            
        Raises:
            AuthenticationError: If secret access fails
            ConfigurationError: If secret doesn't exist
        """
        if not self._use_secret_manager:
            logger.warning(
                "Secret Manager disabled, falling back to environment variables",
                extra={'secret_name': secret_name}
            )
            return os.getenv(secret_name, "")
        
        try:
            # Build the resource name
            name = f"projects/{self.project_id}/secrets/{secret_name}/versions/{version}"
            
            logger.info(
                "Retrieving secret from Secret Manager",
                extra={'secret_name': secret_name, 'version': version}
            )
            
            # Access the secret version
            response = self.client.access_secret_version(request={"name": name})
            
            # Decode the secret payload
            secret_value = response.payload.data.decode("UTF-8")
            
            logger.info(
                "Secret retrieved successfully",
                extra={'secret_name': secret_name, 'length': len(secret_value)}
            )
            
            return secret_value
            
        except gcp_exceptions.NotFound:
            error_msg = f"Secret '{secret_name}' not found in project '{self.project_id}'"
            logger.error(error_msg, extra={'secret_name': secret_name})
            raise ConfigurationError(error_msg)
            
        except gcp_exceptions.PermissionDenied:
            error_msg = f"Permission denied accessing secret '{secret_name}'"
            logger.error(error_msg, extra={'secret_name': secret_name})
            raise AuthenticationError(error_msg)
            
        except Exception as e:
            error_msg = f"Failed to retrieve secret '{secret_name}': {e}"
            logger.error(error_msg, extra={'secret_name': secret_name, 'error': str(e)})
            raise AuthenticationError(error_msg)
    
    @performance_monitor
    async def get_json_secret(self, secret_name: str, version: str = "latest") -> Dict[str, Any]:
        """
        Retrieve a JSON secret from Google Secret Manager.
        
        Args:
            secret_name: Name of the secret containing JSON
            version: Version of the secret (default: "latest")
            
        Returns:
            Parsed JSON as dictionary
            
        Raises:
            AuthenticationError: If secret access fails
            ConfigurationError: If secret doesn't exist or isn't valid JSON
        """
        try:
            secret_value = await self.get_secret(secret_name, version)
            return json.loads(secret_value)
        except json.JSONDecodeError as e:
            error_msg = f"Secret '{secret_name}' contains invalid JSON: {e}"
            logger.error(error_msg, extra={'secret_name': secret_name})
            raise ConfigurationError(error_msg)
    
    @performance_monitor
    async def create_secret(self, secret_name: str, secret_value: str) -> str:
        """
        Create a new secret in Google Secret Manager.
        
        Args:
            secret_name: Name of the secret
            secret_value: Value to store
            
        Returns:
            Resource name of the created secret
            
        Raises:
            AuthenticationError: If secret creation fails
        """
        if not self._use_secret_manager:
            logger.warning("Secret Manager disabled, cannot create secrets")
            return ""
        
        try:
            parent = f"projects/{self.project_id}"
            
            # Create the secret
            secret = self.client.create_secret(
                request={
                    "parent": parent,
                    "secret_id": secret_name,
                    "secret": {"replication": {"automatic": {}}},
                }
            )
            
            # Add the secret version
            version = self.client.add_secret_version(
                request={
                    "parent": secret.name,
                    "payload": {"data": secret_value.encode("UTF-8")},
                }
            )
            
            logger.info(
                "Secret created successfully",
                extra={'secret_name': secret_name, 'version': version.name}
            )
            
            return version.name
            
        except Exception as e:
            error_msg = f"Failed to create secret '{secret_name}': {e}"
            logger.error(error_msg, extra={'secret_name': secret_name, 'error': str(e)})
            raise AuthenticationError(error_msg)


# Global Secret Manager client instance
_secret_manager_client: Optional[SecretManagerClient] = None


def get_secret_manager_client() -> SecretManagerClient:
    """Get or create the global Secret Manager client instance."""
    global _secret_manager_client
    if _secret_manager_client is None:
        _secret_manager_client = SecretManagerClient()
    return _secret_manager_client


@lru_cache(maxsize=128)
def get_cached_secret(secret_name: str) -> str:
    """
    Get a secret with caching for performance.
    
    Args:
        secret_name: Name of the secret
        
    Returns:
        Secret value
    """
    client = get_secret_manager_client()
    # Use asyncio.run for synchronous access
    return asyncio.run(client.get_secret(secret_name))


async def get_jenkins_credentials() -> Dict[str, str]:
    """
    Get Jenkins credentials from Secret Manager or environment variables.
    
    Returns:
        Dictionary with Jenkins credentials
    """
    client = get_secret_manager_client()
    
    try:
        if client._use_secret_manager:
            # Try to get credentials from Secret Manager
            credentials = await client.get_json_secret('jenkins-credentials')
            logger.info("Jenkins credentials retrieved from Secret Manager")
            return credentials
        else:
            # Fall back to environment variables
            credentials = {
                'url': os.getenv('JENKINS_URL', ''),
                'username': os.getenv('JENKINS_USERNAME', ''),
                'password': os.getenv('JENKINS_PASSWORD', '')
            }
            logger.info("Jenkins credentials retrieved from environment variables")
            return credentials
            
    except Exception as e:
        logger.warning(f"Failed to get Jenkins credentials from Secret Manager: {e}")
        # Fall back to environment variables
        credentials = {
            'url': os.getenv('JENKINS_URL', ''),
            'username': os.getenv('JENKINS_USERNAME', ''),
            'password': os.getenv('JENKINS_PASSWORD', '')
        }
        logger.info("Using Jenkins credentials from environment variables as fallback")
        return credentials


async def get_github_credentials() -> Dict[str, str]:
    """
    Get GitHub credentials from Secret Manager or environment variables.
    
    Returns:
        Dictionary with GitHub credentials
    """
    client = get_secret_manager_client()
    
    try:
        if client._use_secret_manager:
            # Try to get credentials from Secret Manager
            credentials = await client.get_json_secret('github-credentials')
            logger.info("GitHub credentials retrieved from Secret Manager")
            return credentials
        else:
            # Fall back to environment variables
            credentials = {
                'token': os.getenv('GITHUB_TOKEN', ''),
                'personal_access_token': os.getenv('GITHUB_PERSONAL_ACCESS_TOKEN', '')
            }
            logger.info("GitHub credentials retrieved from environment variables")
            return credentials
            
    except Exception as e:
        logger.warning(f"Failed to get GitHub credentials from Secret Manager: {e}")
        # Fall back to environment variables
        credentials = {
            'token': os.getenv('GITHUB_TOKEN', ''),
            'personal_access_token': os.getenv('GITHUB_PERSONAL_ACCESS_TOKEN', '')
        }
        logger.info("Using GitHub credentials from environment variables as fallback")
        return credentials


async def setup_secrets_in_secret_manager():
    """
    Setup initial secrets in Google Secret Manager from environment variables.
    This is a utility function for initial deployment.
    """
    client = get_secret_manager_client()
    
    if not client._use_secret_manager:
        logger.info("Secret Manager disabled, skipping secret setup")
        return
    
    try:
        # Setup Jenkins credentials
        jenkins_creds = {
            'url': os.getenv('JENKINS_URL', ''),
            'username': os.getenv('JENKINS_USERNAME', ''),
            'password': os.getenv('JENKINS_PASSWORD', '')
        }
        
        if all(jenkins_creds.values()):
            await client.create_secret('jenkins-credentials', json.dumps(jenkins_creds))
            logger.info("Jenkins credentials stored in Secret Manager")
        
        # Setup GitHub credentials
        github_creds = {
            'token': os.getenv('GITHUB_TOKEN', ''),
            'personal_access_token': os.getenv('GITHUB_PERSONAL_ACCESS_TOKEN', '')
        }
        
        if any(github_creds.values()):
            await client.create_secret('github-credentials', json.dumps(github_creds))
            logger.info("GitHub credentials stored in Secret Manager")
            
    except Exception as e:
        logger.error(f"Failed to setup secrets in Secret Manager: {e}")
        raise
