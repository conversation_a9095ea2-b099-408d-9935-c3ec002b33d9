"""
Input Validators

This module provides comprehensive input validation and sanitization
for the Jenkins Reader Agent to prevent security vulnerabilities.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

import re
import urllib.parse
from typing import Any, Dict, List, Optional, Union
from ipaddress import ip_address, AddressValueError
import html
import bleach

from .exceptions import (
    ValidationError, SecurityViolationError, URLValidationError,
    SSRFViolationError, XSSViolationError, SQLInjectionViolationError,
    FilenameViolationError, InputTooLargeError, InvalidFormatError
)


class URLValidator:
    """Validates URLs and prevents SSRF attacks."""
    
    # Blocked IP ranges for SSRF prevention
    BLOCKED_IP_RANGES = [
        '*********/8',      # Loopback
        '10.0.0.0/8',       # Private Class A
        '**********/12',    # Private Class B
        '***********/16',   # Private Class C
        '***********/16',   # Link-local
        '*********/4',      # Multicast
        '240.0.0.0/4',      # Reserved
        '0.0.0.0/8',        # Current network
    ]
    
    # Allowed schemes
    ALLOWED_SCHEMES = ['http', 'https']
    
    def __init__(self, allow_private_ips: bool = False):
        self.allow_private_ips = allow_private_ips
    
    def validate_url(self, url: str) -> str:
        """Validate URL and prevent SSRF attacks."""
        if not url:
            raise URLValidationError("URL cannot be empty")
        
        try:
            parsed = urllib.parse.urlparse(url)
        except Exception as e:
            raise URLValidationError(f"Invalid URL format: {e}", url=url)
        
        # Check scheme
        if parsed.scheme not in self.ALLOWED_SCHEMES:
            raise URLValidationError(
                f"Scheme '{parsed.scheme}' not allowed. Allowed: {self.ALLOWED_SCHEMES}",
                url=url,
                reason="invalid_scheme"
            )
        
        # Check hostname
        if not parsed.hostname:
            raise URLValidationError("URL must have a hostname", url=url)
        
        # Check for SSRF vulnerabilities
        if not self.allow_private_ips:
            self._check_ssrf(parsed.hostname, url)
        
        # Normalize URL
        return urllib.parse.urlunparse(parsed)
    
    def _check_ssrf(self, hostname: str, original_url: str):
        """Check for SSRF vulnerabilities."""
        try:
            # Try to parse as IP address
            ip = ip_address(hostname)
            
            # Check if IP is in blocked ranges
            for blocked_range in self.BLOCKED_IP_RANGES:
                if ip in ip_address(blocked_range.split('/')[0]):
                    raise SSRFViolationError(
                        f"Access to private IP {ip} is not allowed",
                        url=original_url,
                        blocked_reason=f"IP in blocked range {blocked_range}"
                    )
        
        except AddressValueError:
            # Not an IP address, check for suspicious hostnames
            suspicious_patterns = [
                r'localhost',
                r'127\.0\.0\.1',
                r'0\.0\.0\.0',
                r'::1',
                r'metadata\.google\.internal',
                r'169\.254\.169\.254',  # AWS metadata
            ]
            
            for pattern in suspicious_patterns:
                if re.search(pattern, hostname, re.IGNORECASE):
                    raise SSRFViolationError(
                        f"Hostname '{hostname}' is not allowed",
                        url=original_url,
                        blocked_reason=f"Matches suspicious pattern: {pattern}"
                    )


class JenkinsURLValidator(URLValidator):
    """Specialized URL validator for Jenkins URLs."""
    
    def __init__(self, allow_private_ips: bool = True):
        # Jenkins is often on private networks
        super().__init__(allow_private_ips=allow_private_ips)
    
    def validate_jenkins_url(self, url: str) -> str:
        """Validate Jenkins URL with additional checks."""
        validated_url = self.validate_url(url)
        
        # Additional Jenkins-specific validations
        parsed = urllib.parse.urlparse(validated_url)
        
        # Check for common Jenkins paths
        if parsed.path and not parsed.path.startswith('/'):
            validated_url = validated_url.rstrip('/') + '/'
        
        return validated_url


class InputSanitizer:
    """Sanitizes user input to prevent injection attacks."""
    
    # XSS patterns
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'onload\s*=',
        r'onerror\s*=',
        r'onclick\s*=',
        r'onmouseover\s*=',
        r'<iframe[^>]*>',
        r'<object[^>]*>',
        r'<embed[^>]*>',
    ]
    
    # SQL injection patterns
    SQL_PATTERNS = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
        r'(\b(OR|AND)\s+\d+\s*=\s*\d+)',
        r'(\b(OR|AND)\s+[\'"][^\'"]*[\'"])',
        r'(--|#|/\*|\*/)',
        r'(\bxp_cmdshell\b)',
        r'(\bsp_executesql\b)',
    ]
    
    def sanitize_string(self, value: str, max_length: int = 1000) -> str:
        """Sanitize string input."""
        if not isinstance(value, str):
            raise InvalidFormatError("Value must be a string", value=value)
        
        # Check length
        if len(value) > max_length:
            raise InputTooLargeError(
                f"Input too long: {len(value)} > {max_length}",
                size=len(value),
                max_size=max_length
            )
        
        # Check for XSS
        self._check_xss(value)
        
        # Check for SQL injection
        self._check_sql_injection(value)
        
        # HTML escape
        sanitized = html.escape(value)
        
        # Additional sanitization with bleach
        sanitized = bleach.clean(sanitized, tags=[], attributes={}, strip=True)
        
        return sanitized
    
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename to prevent path traversal."""
        if not filename:
            raise FilenameViolationError("Filename cannot be empty")
        
        # Check for path traversal
        if '..' in filename or '/' in filename or '\\' in filename:
            raise FilenameViolationError(
                "Path traversal detected in filename",
                filename=filename,
                reason="path_traversal"
            )
        
        # Check for dangerous characters
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\0']
        for char in dangerous_chars:
            if char in filename:
                raise FilenameViolationError(
                    f"Dangerous character '{char}' in filename",
                    filename=filename,
                    reason="dangerous_character"
                )
        
        # Remove leading/trailing whitespace and dots
        sanitized = filename.strip(' .')
        
        if not sanitized:
            raise FilenameViolationError("Filename cannot be empty after sanitization")
        
        return sanitized
    
    def _check_xss(self, value: str):
        """Check for XSS patterns."""
        for pattern in self.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise XSSViolationError(
                    f"Potential XSS payload detected: {pattern}",
                    value=value
                )
    
    def _check_sql_injection(self, value: str):
        """Check for SQL injection patterns."""
        for pattern in self.SQL_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise SQLInjectionViolationError(
                    f"Potential SQL injection detected: {pattern}",
                    value=value
                )


class SecurityValidator:
    """Comprehensive security validator."""
    
    def __init__(self):
        self.url_validator = URLValidator()
        self.jenkins_url_validator = JenkinsURLValidator()
        self.input_sanitizer = InputSanitizer()
    
    def validate_jenkins_connection_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate Jenkins connection data."""
        validated = {}
        
        # Validate URL
        if 'url' in data:
            validated['url'] = self.jenkins_url_validator.validate_jenkins_url(data['url'])
        
        # Validate username
        if 'username' in data:
            validated['username'] = self.input_sanitizer.sanitize_string(
                data['username'], max_length=100
            )
        
        # Validate token (don't sanitize, just check length)
        if 'token' in data:
            token = data['token']
            if len(token) > 500:
                raise InputTooLargeError("Token too long", size=len(token), max_size=500)
            validated['token'] = token
        
        return validated
    
    def validate_job_name(self, job_name: str) -> str:
        """Validate Jenkins job name."""
        if not job_name:
            raise ValidationError("Job name cannot be empty")
        
        # Jenkins job names have specific rules
        if not re.match(r'^[a-zA-Z0-9._-]+$', job_name):
            raise InvalidFormatError(
                "Invalid job name format",
                field="job_name",
                value=job_name,
                expected_format="alphanumeric, dots, underscores, hyphens only"
            )
        
        return self.input_sanitizer.sanitize_string(job_name, max_length=200)
    
    def validate_build_number(self, build_number: Union[str, int]) -> int:
        """Validate build number."""
        try:
            build_num = int(build_number)
            if build_num < 1:
                raise ValidationError("Build number must be positive")
            return build_num
        except (ValueError, TypeError):
            raise InvalidFormatError(
                "Invalid build number format",
                field="build_number",
                value=build_number,
                expected_format="positive integer"
            )
