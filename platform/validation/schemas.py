"""
Validation Schemas

This module defines Pydantic schemas for request validation
in the Jenkins Reader Agent.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator, EmailStr
from datetime import datetime

from auth.models import RoleType, AgentType


class JenkinsConnectionRequest(BaseModel):
    """Schema for Jenkins connection requests."""
    url: str = Field(..., description="Jenkins server URL")
    username: Optional[str] = Field(None, description="Jenkins username")
    token: Optional[str] = Field(None, description="Jenkins API token")
    verify_ssl: bool = Field(True, description="Verify SSL certificates")
    timeout: int = Field(30, ge=1, le=300, description="Connection timeout in seconds")
    
    @validator('url')
    def validate_url(cls, v):
        """Validate Jenkins URL."""
        from .input_validators import JenkinsURLValidator
        validator = JenkinsURLValidator()
        return validator.validate_jenkins_url(v)
    
    @validator('username')
    def validate_username(cls, v):
        """Validate username."""
        if v is not None:
            from .input_validators import InputSanitizer
            sanitizer = InputSanitizer()
            return sanitizer.sanitize_string(v, max_length=100)
        return v


class JenkinsJobRequest(BaseModel):
    """Schema for Jenkins job requests."""
    job_name: str = Field(..., description="Jenkins job name")
    include_config: bool = Field(False, description="Include job configuration")
    include_builds: bool = Field(True, description="Include build history")
    max_builds: int = Field(10, ge=1, le=100, description="Maximum builds to retrieve")
    
    @validator('job_name')
    def validate_job_name(cls, v):
        """Validate job name."""
        from .input_validators import SecurityValidator
        validator = SecurityValidator()
        return validator.validate_job_name(v)


class JenkinsAnalysisRequest(BaseModel):
    """Schema for Jenkins analysis requests."""
    analysis_type: str = Field(..., description="Type of analysis to perform")
    job_names: Optional[List[str]] = Field(None, description="Specific jobs to analyze")
    time_range_days: int = Field(30, ge=1, le=365, description="Time range in days")
    include_failed_builds: bool = Field(True, description="Include failed builds")
    include_metrics: bool = Field(True, description="Include performance metrics")
    
    @validator('analysis_type')
    def validate_analysis_type(cls, v):
        """Validate analysis type."""
        allowed_types = [
            'build_trends', 'failure_analysis', 'performance_metrics',
            'dora_metrics', 'pipeline_analysis', 'artifact_analysis'
        ]
        if v not in allowed_types:
            raise ValueError(f'Invalid analysis type. Must be one of: {allowed_types}')
        return v
    
    @validator('job_names')
    def validate_job_names(cls, v):
        """Validate job names."""
        if v is not None:
            from .input_validators import SecurityValidator
            validator = SecurityValidator()
            return [validator.validate_job_name(job_name) for job_name in v]
        return v


class UserCreateRequest(BaseModel):
    """Schema for user creation requests."""
    email: EmailStr = Field(..., description="User email address")
    name: str = Field(..., min_length=1, max_length=100, description="User full name")
    role: RoleType = Field(RoleType.VIEWER, description="User role")
    organization: Optional[str] = Field(None, max_length=100, description="Organization")
    team: Optional[str] = Field(None, max_length=100, description="Team")
    department: Optional[str] = Field(None, max_length=100, description="Department")
    accessible_agents: Optional[List[AgentType]] = Field(
        default_factory=list, description="Agents user can access"
    )
    managed_agents: Optional[List[AgentType]] = Field(
        default_factory=list, description="Agents user can manage"
    )
    
    @validator('name')
    def validate_name(cls, v):
        """Validate user name."""
        from .input_validators import InputSanitizer
        sanitizer = InputSanitizer()
        return sanitizer.sanitize_string(v, max_length=100)
    
    @validator('organization', 'team', 'department')
    def validate_optional_strings(cls, v):
        """Validate optional string fields."""
        if v is not None:
            from .input_validators import InputSanitizer
            sanitizer = InputSanitizer()
            return sanitizer.sanitize_string(v, max_length=100)
        return v


class UserUpdateRequest(BaseModel):
    """Schema for user update requests."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="User full name")
    role: Optional[RoleType] = Field(None, description="User role")
    organization: Optional[str] = Field(None, max_length=100, description="Organization")
    team: Optional[str] = Field(None, max_length=100, description="Team")
    department: Optional[str] = Field(None, max_length=100, description="Department")
    accessible_agents: Optional[List[AgentType]] = Field(None, description="Agents user can access")
    managed_agents: Optional[List[AgentType]] = Field(None, description="Agents user can manage")
    is_active: Optional[bool] = Field(None, description="User active status")
    
    @validator('name')
    def validate_name(cls, v):
        """Validate user name."""
        if v is not None:
            from .input_validators import InputSanitizer
            sanitizer = InputSanitizer()
            return sanitizer.sanitize_string(v, max_length=100)
        return v
    
    @validator('organization', 'team', 'department')
    def validate_optional_strings(cls, v):
        """Validate optional string fields."""
        if v is not None:
            from .input_validators import InputSanitizer
            sanitizer = InputSanitizer()
            return sanitizer.sanitize_string(v, max_length=100)
        return v


class SessionCreateRequest(BaseModel):
    """Schema for session creation requests."""
    user_id: str = Field(..., description="User ID")
    expires_in_minutes: int = Field(60, ge=5, le=1440, description="Session duration in minutes")
    ip_address: Optional[str] = Field(None, description="Client IP address")
    user_agent: Optional[str] = Field(None, max_length=500, description="User agent string")
    
    @validator('user_agent')
    def validate_user_agent(cls, v):
        """Validate user agent."""
        if v is not None:
            from .input_validators import InputSanitizer
            sanitizer = InputSanitizer()
            return sanitizer.sanitize_string(v, max_length=500)
        return v


class AgentRegistrationRequest(BaseModel):
    """Schema for agent registration requests."""
    agent_type: AgentType = Field(..., description="Type of agent")
    name: str = Field(..., min_length=1, max_length=100, description="Agent name")
    description: str = Field(..., min_length=1, max_length=500, description="Agent description")
    version: str = Field(..., description="Agent version")
    capabilities: List[str] = Field(default_factory=list, description="Agent capabilities")
    supported_operations: List[str] = Field(default_factory=list, description="Supported operations")
    endpoint_url: Optional[str] = Field(None, description="Agent endpoint URL")
    health_check_url: Optional[str] = Field(None, description="Health check URL")
    
    @validator('name', 'description')
    def validate_strings(cls, v):
        """Validate string fields."""
        from .input_validators import InputSanitizer
        sanitizer = InputSanitizer()
        max_length = 100 if len(v) <= 100 else 500
        return sanitizer.sanitize_string(v, max_length=max_length)
    
    @validator('endpoint_url', 'health_check_url')
    def validate_urls(cls, v):
        """Validate URL fields."""
        if v is not None:
            from .input_validators import URLValidator
            validator = URLValidator()
            return validator.validate_url(v)
        return v
    
    @validator('capabilities', 'supported_operations')
    def validate_string_lists(cls, v):
        """Validate string list fields."""
        from .input_validators import InputSanitizer
        sanitizer = InputSanitizer()
        return [sanitizer.sanitize_string(item, max_length=100) for item in v]


class AgentCommunicationRequest(BaseModel):
    """Schema for agent communication requests."""
    target_agent: AgentType = Field(..., description="Target agent type")
    message_type: str = Field(..., description="Message type")
    payload: Dict[str, Any] = Field(..., description="Message payload")
    encrypted: bool = Field(False, description="Whether message should be encrypted")
    priority: str = Field("normal", description="Message priority")
    
    @validator('message_type')
    def validate_message_type(cls, v):
        """Validate message type."""
        allowed_types = [
            'data_collection_request', 'analysis_request', 'status_update',
            'health_check', 'configuration_update', 'shutdown_request'
        ]
        if v not in allowed_types:
            raise ValueError(f'Invalid message type. Must be one of: {allowed_types}')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        """Validate priority."""
        allowed_priorities = ['low', 'normal', 'high', 'urgent']
        if v not in allowed_priorities:
            raise ValueError(f'Invalid priority. Must be one of: {allowed_priorities}')
        return v


class BuildAnalysisRequest(BaseModel):
    """Schema for build analysis requests."""
    job_name: str = Field(..., description="Jenkins job name")
    build_number: Optional[int] = Field(None, description="Specific build number")
    include_artifacts: bool = Field(False, description="Include build artifacts")
    include_logs: bool = Field(False, description="Include build logs")
    include_test_results: bool = Field(True, description="Include test results")
    
    @validator('job_name')
    def validate_job_name(cls, v):
        """Validate job name."""
        from .input_validators import SecurityValidator
        validator = SecurityValidator()
        return validator.validate_job_name(v)
    
    @validator('build_number')
    def validate_build_number(cls, v):
        """Validate build number."""
        if v is not None:
            from .input_validators import SecurityValidator
            validator = SecurityValidator()
            return validator.validate_build_number(v)
        return v
