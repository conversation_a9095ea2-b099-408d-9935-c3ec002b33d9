"""
Authentication and Authorization Data Models

This module defines Pydantic models for authentication and authorization
data structures used throughout the Jenkins Reader Agent multi-agent system.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Set
from enum import Enum
from pydantic import BaseModel, Field, EmailStr, validator
import uuid


class AgentType(str, Enum):
    """Enumeration of agent types in the multi-agent architecture."""
    # Top Level
    MANAGER_ORCHESTRATOR = "manager_orchestrator"
    
    # Industry Field Agents (Analysis Layer)
    DORA_METRICS_AGENT = "dora_metrics_agent"
    SALES_ANALYST_AGENT = "sales_analyst_agent"
    MARKETING_ANALYST_AGENT = "marketing_analyst_agent"
    SECURITY_ANALYST_AGENT = "security_analyst_agent"
    PERFORMANCE_ANALYST_AGENT = "performance_analyst_agent"
    
    # SAAS Agents (Data Layer - Read-Only by Design)
    JENKINS_AGENT = "jenkins_agent"
    GITHUB_AGENT = "github_agent"
    JFROG_AGENT = "jfrog_agent"
    SLACK_AGENT = "slack_agent"
    JIRA_AGENT = "jira_agent"
    CONFLUENCE_AGENT = "confluence_agent"
    SALESFORCE_AGENT = "salesforce_agent"
    HUBSPOT_AGENT = "hubspot_agent"


class RoleType(str, Enum):
    """Enumeration of available user roles."""
    # System Roles
    SUPER_ADMIN = "super_admin"
    PLATFORM_ADMIN = "platform_admin"
    
    # Agent Management Roles
    AGENT_ORCHESTRATOR = "agent_orchestrator"
    AGENT_MANAGER = "agent_manager"
    
    # Analysis Roles
    SENIOR_ANALYST = "senior_analyst"
    ANALYST = "analyst"
    JUNIOR_ANALYST = "junior_analyst"
    
    # Viewer Roles
    EXECUTIVE_VIEWER = "executive_viewer"
    MANAGER_VIEWER = "manager_viewer"
    VIEWER = "viewer"
    
    # Service Accounts (for inter-agent communication)
    SERVICE_ACCOUNT = "service_account"
    AGENT_SERVICE = "agent_service"


class PermissionType(str, Enum):
    """Enumeration of available permissions."""
    
    # System Administration
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    PLATFORM_MANAGE = "platform:manage"
    
    # Agent Management
    AGENT_ORCHESTRATE = "agent:orchestrate"
    AGENT_MANAGE = "agent:manage"
    AGENT_DEPLOY = "agent:deploy"
    AGENT_CONFIGURE = "agent:configure"
    AGENT_MONITOR = "agent:monitor"
    
    # Multi-Agent Coordination
    CROSS_AGENT_READ = "cross_agent:read"
    CROSS_AGENT_COORDINATE = "cross_agent:coordinate"
    AGENT_COMMUNICATION = "agent:communication"
    
    # SAAS Agent Permissions (Read-Only by Design)
    JENKINS_READ = "jenkins:read"
    GITHUB_READ = "github:read"
    JFROG_READ = "jfrog:read"
    SLACK_READ = "slack:read"
    JIRA_READ = "jira:read"
    CONFLUENCE_READ = "confluence:read"
    SALESFORCE_READ = "salesforce:read"
    HUBSPOT_READ = "hubspot:read"
    
    # Analysis Agent Permissions
    DORA_ANALYZE = "dora:analyze"
    SALES_ANALYZE = "sales:analyze"
    MARKETING_ANALYZE = "marketing:analyze"
    SECURITY_ANALYZE = "security:analyze"
    PERFORMANCE_ANALYZE = "performance:analyze"
    
    # Data Access Levels
    DATA_READ = "data:read"
    DATA_AGGREGATE = "data:aggregate"
    DATA_EXPORT = "data:export"
    
    # Reporting & Insights
    REPORT_GENERATE = "report:generate"
    INSIGHT_CREATE = "insight:create"
    DASHBOARD_VIEW = "dashboard:view"
    DASHBOARD_CREATE = "dashboard:create"
    
    # User Management
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"
    
    # Session Management
    SESSION_READ = "session:read"
    SESSION_WRITE = "session:write"
    SESSION_DELETE = "session:delete"


class Permission(BaseModel):
    """Permission model."""
    name: PermissionType
    description: str
    resource: Optional[str] = None
    
    class Config:
        use_enum_values = True


class Role(BaseModel):
    """Role model with associated permissions."""
    name: RoleType
    description: str
    permissions: Set[PermissionType] = Field(default_factory=set)
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        use_enum_values = True


class User(BaseModel):
    """User model for multi-agent architecture."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    email: EmailStr
    name: str
    role: RoleType = RoleType.VIEWER
    
    # Multi-Agent Architecture Fields
    agent_type: Optional[AgentType] = None  # For service accounts representing agents
    is_service_account: bool = False
    managed_agents: List[AgentType] = Field(default_factory=list)  # Agents this user can manage
    accessible_agents: List[AgentType] = Field(default_factory=list)  # Agents this user can access
    
    # Organization & Team
    organization: Optional[str] = None
    team: Optional[str] = None
    department: Optional[str] = None
    
    # Status fields
    is_active: bool = True
    is_verified: bool = False
    last_login: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # OAuth2 fields
    google_id: Optional[str] = None
    picture_url: Optional[str] = None
    
    # Security fields
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    
    # Agent-specific fields
    agent_version: Optional[str] = None
    agent_capabilities: List[str] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True
    
    @validator('email')
    def validate_email(cls, v):
        """Validate email format."""
        if not v or '@' not in v:
            raise ValueError('Invalid email format')
        return v.lower()
    
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def has_permission(self, permission: PermissionType) -> bool:
        """Check if user has specific permission."""
        role_permissions = get_role_permissions(self.role)
        return permission in role_permissions
    
    def can_access_agent(self, agent_type: AgentType) -> bool:
        """Check if user can access specific agent."""
        if self.role in [RoleType.SUPER_ADMIN, RoleType.PLATFORM_ADMIN]:
            return True
        
        if self.role == RoleType.AGENT_ORCHESTRATOR:
            return True  # Can access all agents
        
        return agent_type in self.accessible_agents
    
    def can_manage_agent(self, agent_type: AgentType) -> bool:
        """Check if user can manage specific agent."""
        if self.role in [RoleType.SUPER_ADMIN, RoleType.PLATFORM_ADMIN]:
            return True
        
        if self.role == RoleType.AGENT_ORCHESTRATOR:
            return True  # Can manage all agents
        
        if self.role == RoleType.AGENT_MANAGER:
            return agent_type in self.managed_agents
        
        return False
    
    def is_agent_service_account(self) -> bool:
        """Check if this is an agent service account."""
        return self.is_service_account and self.agent_type is not None


class AuthToken(BaseModel):
    """Authentication token model."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds
    expires_at: datetime
    scope: Optional[str] = None
    
    @validator('expires_at', pre=True, always=True)
    def set_expires_at(cls, v, values):
        """Set expiration time based on expires_in."""
        if v is None and 'expires_in' in values:
            return datetime.utcnow() + timedelta(seconds=values['expires_in'])
        return v
    
    def is_expired(self) -> bool:
        """Check if token is expired."""
        return datetime.utcnow() >= self.expires_at


class RefreshToken(BaseModel):
    """Refresh token model."""
    refresh_token: str
    user_id: str
    expires_at: datetime
    is_revoked: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    def is_expired(self) -> bool:
        """Check if refresh token is expired."""
        return datetime.utcnow() >= self.expires_at
    
    def is_valid(self) -> bool:
        """Check if refresh token is valid."""
        return not self.is_revoked and not self.is_expired()


class UserRole(BaseModel):
    """User role assignment model."""
    user_id: str
    role: RoleType
    assigned_by: str
    assigned_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    is_active: bool = True
    
    class Config:
        use_enum_values = True


class AuthRequest(BaseModel):
    """Authentication request model."""
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    grant_type: str = "authorization_code"
    code: Optional[str] = None  # OAuth2 authorization code
    redirect_uri: Optional[str] = None
    client_id: Optional[str] = None
    scope: Optional[str] = None
    
    @validator('grant_type')
    def validate_grant_type(cls, v):
        """Validate grant type."""
        allowed_types = ['authorization_code', 'refresh_token', 'client_credentials']
        if v not in allowed_types:
            raise ValueError(f'Invalid grant type. Must be one of: {allowed_types}')
        return v


class AuthResponse(BaseModel):
    """Authentication response model."""
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: int
    scope: Optional[str] = None
    user: User


class TokenClaims(BaseModel):
    """JWT token claims model."""
    sub: str  # subject (user ID)
    email: str
    name: str
    role: RoleType
    iat: int  # issued at
    exp: int  # expiration time
    iss: str = "jenkins-reader-agent"  # issuer
    aud: str = "jenkins-reader-agent"  # audience
    jti: str = Field(default_factory=lambda: str(uuid.uuid4()))  # JWT ID
    
    class Config:
        use_enum_values = True


class SessionData(BaseModel):
    """Session data model."""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_accessed: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    is_active: bool = True
    data: Dict[str, Any] = Field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() >= self.expires_at
    
    def is_valid(self) -> bool:
        """Check if session is valid."""
        return self.is_active and not self.is_expired()


class AgentRegistration(BaseModel):
    """Agent registration model for multi-agent architecture."""
    agent_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    agent_type: AgentType
    name: str
    description: str
    version: str
    
    # Agent capabilities
    capabilities: List[str] = Field(default_factory=list)
    supported_operations: List[str] = Field(default_factory=list)
    
    # Connection details
    endpoint_url: Optional[str] = None
    health_check_url: Optional[str] = None
    
    # Status
    is_active: bool = True
    is_healthy: bool = True
    last_health_check: Optional[datetime] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str  # User ID who registered the agent
    
    # Security
    api_key: Optional[str] = None
    allowed_ips: List[str] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True


class AgentCommunication(BaseModel):
    """Model for inter-agent communication."""
    communication_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    source_agent: AgentType
    target_agent: AgentType
    message_type: str
    payload: Dict[str, Any]
    
    # Status
    status: str = "pending"  # pending, sent, received, processed, failed
    created_at: datetime = Field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    
    # Security
    encrypted: bool = False
    signature: Optional[str] = None
    
    class Config:
        use_enum_values = True


# Role permission mappings for multi-agent architecture
ROLE_PERMISSIONS = {
    # System Administration Roles
    RoleType.SUPER_ADMIN: {
        # Full system access
        PermissionType.SYSTEM_ADMIN,
        PermissionType.SYSTEM_CONFIG,
        PermissionType.SYSTEM_MONITOR,
        PermissionType.PLATFORM_MANAGE,

        # Full agent management
        PermissionType.AGENT_ORCHESTRATE,
        PermissionType.AGENT_MANAGE,
        PermissionType.AGENT_DEPLOY,
        PermissionType.AGENT_CONFIGURE,
        PermissionType.AGENT_MONITOR,

        # Cross-agent coordination
        PermissionType.CROSS_AGENT_READ,
        PermissionType.CROSS_AGENT_COORDINATE,
        PermissionType.AGENT_COMMUNICATION,

        # All SAAS agent access
        PermissionType.JENKINS_READ,
        PermissionType.GITHUB_READ,
        PermissionType.JFROG_READ,
        PermissionType.SLACK_READ,
        PermissionType.JIRA_READ,
        PermissionType.CONFLUENCE_READ,
        PermissionType.SALESFORCE_READ,
        PermissionType.HUBSPOT_READ,

        # All analysis capabilities
        PermissionType.DORA_ANALYZE,
        PermissionType.SALES_ANALYZE,
        PermissionType.MARKETING_ANALYZE,
        PermissionType.SECURITY_ANALYZE,
        PermissionType.PERFORMANCE_ANALYZE,

        # Full data access
        PermissionType.DATA_READ,
        PermissionType.DATA_AGGREGATE,
        PermissionType.DATA_EXPORT,

        # Full reporting
        PermissionType.REPORT_GENERATE,
        PermissionType.INSIGHT_CREATE,
        PermissionType.DASHBOARD_VIEW,
        PermissionType.DASHBOARD_CREATE,

        # User management
        PermissionType.USER_READ,
        PermissionType.USER_WRITE,
        PermissionType.USER_DELETE,

        # Session management
        PermissionType.SESSION_READ,
        PermissionType.SESSION_WRITE,
        PermissionType.SESSION_DELETE,
    },

    RoleType.PLATFORM_ADMIN: {
        # Platform management
        PermissionType.PLATFORM_MANAGE,
        PermissionType.SYSTEM_CONFIG,
        PermissionType.SYSTEM_MONITOR,

        # Agent management
        PermissionType.AGENT_MANAGE,
        PermissionType.AGENT_DEPLOY,
        PermissionType.AGENT_CONFIGURE,
        PermissionType.AGENT_MONITOR,

        # Cross-agent coordination
        PermissionType.CROSS_AGENT_READ,
        PermissionType.CROSS_AGENT_COORDINATE,

        # All SAAS agent access
        PermissionType.JENKINS_READ,
        PermissionType.GITHUB_READ,
        PermissionType.JFROG_READ,
        PermissionType.SLACK_READ,
        PermissionType.JIRA_READ,
        PermissionType.CONFLUENCE_READ,
        PermissionType.SALESFORCE_READ,
        PermissionType.HUBSPOT_READ,

        # Data access
        PermissionType.DATA_READ,
        PermissionType.DATA_AGGREGATE,
        PermissionType.DATA_EXPORT,

        # User management
        PermissionType.USER_READ,
        PermissionType.USER_WRITE,

        # Session management
        PermissionType.SESSION_READ,
        PermissionType.SESSION_WRITE,
    },

    # Agent Management Roles
    RoleType.AGENT_ORCHESTRATOR: {
        # Agent orchestration
        PermissionType.AGENT_ORCHESTRATE,
        PermissionType.AGENT_MANAGE,
        PermissionType.AGENT_MONITOR,

        # Cross-agent coordination
        PermissionType.CROSS_AGENT_READ,
        PermissionType.CROSS_AGENT_COORDINATE,
        PermissionType.AGENT_COMMUNICATION,

        # All SAAS agent access
        PermissionType.JENKINS_READ,
        PermissionType.GITHUB_READ,
        PermissionType.JFROG_READ,
        PermissionType.SLACK_READ,
        PermissionType.JIRA_READ,
        PermissionType.CONFLUENCE_READ,
        PermissionType.SALESFORCE_READ,
        PermissionType.HUBSPOT_READ,

        # All analysis capabilities
        PermissionType.DORA_ANALYZE,
        PermissionType.SALES_ANALYZE,
        PermissionType.MARKETING_ANALYZE,
        PermissionType.SECURITY_ANALYZE,
        PermissionType.PERFORMANCE_ANALYZE,

        # Data access
        PermissionType.DATA_READ,
        PermissionType.DATA_AGGREGATE,

        # Reporting
        PermissionType.REPORT_GENERATE,
        PermissionType.INSIGHT_CREATE,
        PermissionType.DASHBOARD_VIEW,
        PermissionType.DASHBOARD_CREATE,
    },

    RoleType.AGENT_MANAGER: {
        # Limited agent management
        PermissionType.AGENT_MANAGE,
        PermissionType.AGENT_MONITOR,

        # Cross-agent read access
        PermissionType.CROSS_AGENT_READ,

        # SAAS agent access (based on managed agents)
        PermissionType.DATA_READ,
        PermissionType.DATA_AGGREGATE,

        # Reporting
        PermissionType.REPORT_GENERATE,
        PermissionType.DASHBOARD_VIEW,
        PermissionType.DASHBOARD_CREATE,
    },

    # Analysis Roles
    RoleType.SENIOR_ANALYST: {
        # Cross-agent read access
        PermissionType.CROSS_AGENT_READ,

        # All SAAS agent read access
        PermissionType.JENKINS_READ,
        PermissionType.GITHUB_READ,
        PermissionType.JFROG_READ,
        PermissionType.SLACK_READ,
        PermissionType.JIRA_READ,
        PermissionType.CONFLUENCE_READ,
        PermissionType.SALESFORCE_READ,
        PermissionType.HUBSPOT_READ,

        # All analysis capabilities
        PermissionType.DORA_ANALYZE,
        PermissionType.SALES_ANALYZE,
        PermissionType.MARKETING_ANALYZE,
        PermissionType.SECURITY_ANALYZE,
        PermissionType.PERFORMANCE_ANALYZE,

        # Data access
        PermissionType.DATA_READ,
        PermissionType.DATA_AGGREGATE,
        PermissionType.DATA_EXPORT,

        # Reporting
        PermissionType.REPORT_GENERATE,
        PermissionType.INSIGHT_CREATE,
        PermissionType.DASHBOARD_VIEW,
        PermissionType.DASHBOARD_CREATE,
    },

    RoleType.ANALYST: {
        # Limited SAAS agent access
        PermissionType.JENKINS_READ,
        PermissionType.GITHUB_READ,
        PermissionType.JIRA_READ,

        # Specific analysis capabilities (based on specialization)
        PermissionType.DORA_ANALYZE,

        # Data access
        PermissionType.DATA_READ,
        PermissionType.DATA_AGGREGATE,

        # Reporting
        PermissionType.REPORT_GENERATE,
        PermissionType.DASHBOARD_VIEW,
    },

    RoleType.JUNIOR_ANALYST: {
        # Limited SAAS agent access
        PermissionType.JENKINS_READ,
        PermissionType.GITHUB_READ,

        # Data read only
        PermissionType.DATA_READ,

        # Basic reporting
        PermissionType.DASHBOARD_VIEW,
    },

    # Viewer Roles
    RoleType.EXECUTIVE_VIEWER: {
        # High-level dashboard access
        PermissionType.DASHBOARD_VIEW,
        PermissionType.REPORT_GENERATE,

        # Data read access
        PermissionType.DATA_READ,

        # Cross-agent read for insights
        PermissionType.CROSS_AGENT_READ,
    },

    RoleType.MANAGER_VIEWER: {
        # Dashboard access
        PermissionType.DASHBOARD_VIEW,

        # Data read access
        PermissionType.DATA_READ,

        # Limited SAAS agent access
        PermissionType.JENKINS_READ,
        PermissionType.GITHUB_READ,
    },

    RoleType.VIEWER: {
        # Basic dashboard access
        PermissionType.DASHBOARD_VIEW,

        # Basic data read
        PermissionType.DATA_READ,
    },

    # Service Account Roles
    RoleType.SERVICE_ACCOUNT: {
        # Agent communication
        PermissionType.AGENT_COMMUNICATION,

        # Cross-agent coordination
        PermissionType.CROSS_AGENT_READ,
        PermissionType.CROSS_AGENT_COORDINATE,

        # Data access for processing
        PermissionType.DATA_READ,
        PermissionType.DATA_AGGREGATE,
    },

    RoleType.AGENT_SERVICE: {
        # All SAAS agent access (read-only by design)
        PermissionType.JENKINS_READ,
        PermissionType.GITHUB_READ,
        PermissionType.JFROG_READ,
        PermissionType.SLACK_READ,
        PermissionType.JIRA_READ,
        PermissionType.CONFLUENCE_READ,
        PermissionType.SALESFORCE_READ,
        PermissionType.HUBSPOT_READ,

        # Agent communication
        PermissionType.AGENT_COMMUNICATION,

        # Data processing
        PermissionType.DATA_READ,
        PermissionType.DATA_AGGREGATE,

        # Analysis capabilities (based on agent type)
        PermissionType.DORA_ANALYZE,
        PermissionType.SALES_ANALYZE,
        PermissionType.MARKETING_ANALYZE,
        PermissionType.SECURITY_ANALYZE,
        PermissionType.PERFORMANCE_ANALYZE,
    },
}


def get_role_permissions(role: RoleType) -> Set[PermissionType]:
    """Get permissions for a specific role."""
    return ROLE_PERMISSIONS.get(role, set())


def create_default_roles() -> List[Role]:
    """Create default system roles for multi-agent architecture."""
    return [
        # System Administration
        Role(
            name=RoleType.SUPER_ADMIN,
            description="Super administrator with full system access",
            permissions=ROLE_PERMISSIONS[RoleType.SUPER_ADMIN]
        ),
        Role(
            name=RoleType.PLATFORM_ADMIN,
            description="Platform administrator with agent management capabilities",
            permissions=ROLE_PERMISSIONS[RoleType.PLATFORM_ADMIN]
        ),

        # Agent Management
        Role(
            name=RoleType.AGENT_ORCHESTRATOR,
            description="Agent orchestrator with full agent coordination capabilities",
            permissions=ROLE_PERMISSIONS[RoleType.AGENT_ORCHESTRATOR]
        ),
        Role(
            name=RoleType.AGENT_MANAGER,
            description="Agent manager with limited agent management capabilities",
            permissions=ROLE_PERMISSIONS[RoleType.AGENT_MANAGER]
        ),

        # Analysis Roles
        Role(
            name=RoleType.SENIOR_ANALYST,
            description="Senior analyst with full analysis capabilities across all agents",
            permissions=ROLE_PERMISSIONS[RoleType.SENIOR_ANALYST]
        ),
        Role(
            name=RoleType.ANALYST,
            description="Analyst with specific domain analysis capabilities",
            permissions=ROLE_PERMISSIONS[RoleType.ANALYST]
        ),
        Role(
            name=RoleType.JUNIOR_ANALYST,
            description="Junior analyst with limited analysis capabilities",
            permissions=ROLE_PERMISSIONS[RoleType.JUNIOR_ANALYST]
        ),

        # Viewer Roles
        Role(
            name=RoleType.EXECUTIVE_VIEWER,
            description="Executive viewer with high-level dashboard access",
            permissions=ROLE_PERMISSIONS[RoleType.EXECUTIVE_VIEWER]
        ),
        Role(
            name=RoleType.MANAGER_VIEWER,
            description="Manager viewer with departmental dashboard access",
            permissions=ROLE_PERMISSIONS[RoleType.MANAGER_VIEWER]
        ),
        Role(
            name=RoleType.VIEWER,
            description="Basic viewer with read-only dashboard access",
            permissions=ROLE_PERMISSIONS[RoleType.VIEWER]
        ),

        # Service Accounts
        Role(
            name=RoleType.SERVICE_ACCOUNT,
            description="Service account for system integrations",
            permissions=ROLE_PERMISSIONS[RoleType.SERVICE_ACCOUNT]
        ),
        Role(
            name=RoleType.AGENT_SERVICE,
            description="Agent service account for inter-agent communication",
            permissions=ROLE_PERMISSIONS[RoleType.AGENT_SERVICE]
        ),
    ]


def get_agent_default_permissions(agent_type: AgentType) -> Set[PermissionType]:
    """Get default permissions for specific agent types."""
    # All SAAS agents have read-only permissions by design
    saas_permissions = {
        PermissionType.DATA_READ,
        PermissionType.AGENT_COMMUNICATION,
    }

    # Agent-specific permissions
    agent_permissions = {
        AgentType.JENKINS_AGENT: {PermissionType.JENKINS_READ},
        AgentType.GITHUB_AGENT: {PermissionType.GITHUB_READ},
        AgentType.JFROG_AGENT: {PermissionType.JFROG_READ},
        AgentType.SLACK_AGENT: {PermissionType.SLACK_READ},
        AgentType.JIRA_AGENT: {PermissionType.JIRA_READ},
        AgentType.CONFLUENCE_AGENT: {PermissionType.CONFLUENCE_READ},
        AgentType.SALESFORCE_AGENT: {PermissionType.SALESFORCE_READ},
        AgentType.HUBSPOT_AGENT: {PermissionType.HUBSPOT_READ},

        # Analysis agents
        AgentType.DORA_METRICS_AGENT: {
            PermissionType.DORA_ANALYZE,
            PermissionType.JENKINS_READ,
            PermissionType.GITHUB_READ,
            PermissionType.DATA_AGGREGATE,
        },
        AgentType.SALES_ANALYST_AGENT: {
            PermissionType.SALES_ANALYZE,
            PermissionType.SALESFORCE_READ,
            PermissionType.HUBSPOT_READ,
            PermissionType.DATA_AGGREGATE,
        },
        AgentType.MARKETING_ANALYST_AGENT: {
            PermissionType.MARKETING_ANALYZE,
            PermissionType.HUBSPOT_READ,
            PermissionType.SLACK_READ,
            PermissionType.DATA_AGGREGATE,
        },
        AgentType.SECURITY_ANALYST_AGENT: {
            PermissionType.SECURITY_ANALYZE,
            PermissionType.JENKINS_READ,
            PermissionType.GITHUB_READ,
            PermissionType.JFROG_READ,
            PermissionType.DATA_AGGREGATE,
        },
        AgentType.PERFORMANCE_ANALYST_AGENT: {
            PermissionType.PERFORMANCE_ANALYZE,
            PermissionType.JENKINS_READ,
            PermissionType.GITHUB_READ,
            PermissionType.DATA_AGGREGATE,
        },

        # Manager/Orchestrator
        AgentType.MANAGER_ORCHESTRATOR: {
            PermissionType.AGENT_ORCHESTRATE,
            PermissionType.CROSS_AGENT_COORDINATE,
            PermissionType.CROSS_AGENT_READ,
            PermissionType.DATA_AGGREGATE,
        },
    }

    return saas_permissions.union(agent_permissions.get(agent_type, set()))
