"""
Authorization Service

This module provides role-based access control (RBAC) for the Jenkins Reader Agent
multi-agent system.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from typing import Set, List, Optional
from .models import User, Role, RoleType, PermissionType, get_role_permissions, create_default_roles
from .exceptions import AuthorizationError, InsufficientPermissionsError


class PermissionChecker:
    """Permission checking utilities."""
    
    @staticmethod
    def has_permission(user: User, permission: PermissionType) -> bool:
        """Check if user has specific permission."""
        if not user.is_active:
            return False
        
        user_permissions = get_role_permissions(user.role)
        return permission in user_permissions
    
    @staticmethod
    def has_any_permission(user: User, permissions: List[PermissionType]) -> bool:
        """Check if user has any of the specified permissions."""
        user_permissions = get_role_permissions(user.role)
        return any(perm in user_permissions for perm in permissions)


class RoleManager:
    """Role management service."""
    
    def __init__(self):
        self._roles = {}
        self._initialize_default_roles()
    
    def _initialize_default_roles(self):
        """Initialize default system roles."""
        default_roles = create_default_roles()
        for role in default_roles:
            self._roles[role.name] = role
    
    def get_role(self, role_name: RoleType) -> Role:
        """Get role by name."""
        return self._roles.get(role_name)
    
    def get_all_roles(self) -> List[Role]:
        """Get all available roles."""
        return list(self._roles.values())


class AuthorizationService:
    """Main authorization service."""
    
    def __init__(self):
        self.role_manager = RoleManager()
        self.permission_checker = PermissionChecker()
    
    def authorize_user(self, user: User, required_permission: PermissionType) -> bool:
        """Authorize user for specific permission."""
        if not user.is_active:
            raise AuthorizationError("User account is disabled")
        
        if not self.permission_checker.has_permission(user, required_permission):
            raise InsufficientPermissionsError(
                f"User lacks required permission: {required_permission}",
                required_permission=required_permission.value,
                user_role=user.role.value
            )
        
        return True
    
    def get_user_permissions(self, user: User) -> Set[PermissionType]:
        """Get all permissions for user."""
        return get_role_permissions(user.role)


# Permission decorator
Permission = PermissionType  # Alias for compatibility
