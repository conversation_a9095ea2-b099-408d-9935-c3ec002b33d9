"""
Agent Management Service

This module provides agent management for the multi-agent architecture.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

import secrets
from typing import Optional, Dict, List
from datetime import datetime

from .models import User, AgentType, AgentRegistration, AgentCommunication, RoleType
from .exceptions import AuthenticationError


class AgentAuthenticationService:
    """Service for agent authentication and API key management."""
    
    def __init__(self):
        self._agents: Dict[str, AgentRegistration] = {}
        self._api_keys: Dict[str, str] = {}  # api_key -> agent_id
        self._agent_users: Dict[str, User] = {}  # agent_id -> User
    
    def register_agent(self, agent_type: AgentType, name: str, description: str, 
                      version: str, capabilities: List[str], created_by: str) -> AgentRegistration:
        """Register a new agent in the system."""
        import uuid
        
        agent_id = str(uuid.uuid4())
        api_key = f"agent_{secrets.token_urlsafe(16)}"
        
        registration = AgentRegistration(
            agent_id=agent_id,
            agent_type=agent_type,
            name=name,
            description=description,
            version=version,
            capabilities=capabilities,
            created_by=created_by,
            api_key=api_key
        )
        
        self._agents[agent_id] = registration
        self._api_keys[api_key] = agent_id
        
        # Create service account user for the agent
        agent_user = User(
            email=f"{name.lower().replace(' ', '_')}@agents.system",
            name=f"{name} Service Account",
            role=RoleType.AGENT_SERVICE,
            agent_type=agent_type,
            is_service_account=True
        )
        self._agent_users[agent_id] = agent_user
        
        return registration
    
    def authenticate_agent(self, api_key: str) -> Optional[User]:
        """Authenticate agent using API key and return agent user."""
        agent_id = self._api_keys.get(api_key)
        if not agent_id:
            return None
        
        agent_registration = self._agents.get(agent_id)
        if not agent_registration or not agent_registration.is_active:
            return None
        
        return self._agent_users.get(agent_id)
    
    def get_all_agents(self) -> List[AgentRegistration]:
        """Get all registered agents."""
        return list(self._agents.values())


class AgentCommunicationService:
    """Service for managing inter-agent communication."""
    
    def __init__(self):
        self._communications: Dict[str, AgentCommunication] = {}
    
    def send_message(self, source_agent: AgentType, target_agent: AgentType, 
                    message_type: str, payload: Dict) -> AgentCommunication:
        """Send message from one agent to another."""
        communication = AgentCommunication(
            source_agent=source_agent,
            target_agent=target_agent,
            message_type=message_type,
            payload=payload
        )
        
        self._communications[communication.communication_id] = communication
        communication.status = "sent"
        
        return communication


class AgentOrchestrationService:
    """Service for orchestrating multiple agents."""
    
    def __init__(self):
        self.agent_auth_service = AgentAuthenticationService()
        self.communication_service = AgentCommunicationService()
    
    def orchestrate_analysis(self, analysis_type: str, data_sources: List[AgentType], 
                           target_agents: List[AgentType]) -> Dict[str, AgentCommunication]:
        """Orchestrate analysis across multiple agents."""
        communications = {}
        
        # Send data collection requests to SAAS agents
        for saas_agent in data_sources:
            comm = self.communication_service.send_message(
                source_agent=AgentType.MANAGER_ORCHESTRATOR,
                target_agent=saas_agent,
                message_type="data_collection_request",
                payload={"analysis_type": analysis_type, "timestamp": datetime.utcnow().isoformat()}
            )
            communications[f"data_collection_{saas_agent}"] = comm
        
        # Send analysis requests to analysis agents
        for analysis_agent in target_agents:
            comm = self.communication_service.send_message(
                source_agent=AgentType.MANAGER_ORCHESTRATOR,
                target_agent=analysis_agent,
                message_type="analysis_request",
                payload={
                    "analysis_type": analysis_type,
                    "data_sources": [agent.value for agent in data_sources],
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            communications[f"analysis_{analysis_agent}"] = comm
        
        return communications
