# ADK Streaming Support

This document explains how to configure and use streaming responses in the ADK Analyst platform for real-time token-by-token responses.

## 🚀 What is Streaming?

Streaming provides real-time, token-by-token responses instead of waiting for the complete response. This significantly improves user experience, especially for longer responses.

### Benefits
- **Improved UX**: Users see responses as they're generated
- **Reduced Perceived Latency**: Responses feel faster even if total time is the same
- **Better Engagement**: Users can start reading while the response is being generated
- **Real-time Feedback**: Immediate indication that the system is working

## ⚙️ Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# ============================================================================
# Streaming Configuration
# ============================================================================
ENABLE_STREAMING=true
STREAM_DELAY_MS=50
STREAM_BUFFER_SIZE=1024
STREAM_CHUNK_SIZE=256
STREAM_TIMEOUT_SECONDS=30
```

### Configuration Options

| Variable | Description | Default | Range |
|----------|-------------|---------|-------|
| `ENABLE_STREAMING` | Enable/disable streaming | `true` | `true`/`false` |
| `STREAM_DELAY_MS` | Delay between tokens (ms) | `50` | `0-1000` |
| `STREAM_BUFFER_SIZE` | Buffer size for streaming | `1024` | `256-8192` |
| `STREAM_CHUNK_SIZE` | Chunk size for responses | `256` | `64-2048` |
| `STREAM_TIMEOUT_SECONDS` | Streaming timeout | `30` | `10-300` |

## 🔧 ADK Configuration

### Agent Configuration (adk.yaml)

```yaml
# Agent configuration
agent:
  module: "jenkins_agent.agent"
  root_agent: "root_agent"
  streaming: true  # Enable streaming responses
  models:
    - name: "gemini-2.5-pro"
      display_name: "Gemini 2.5 Pro"
      description: "Advanced model for complex analysis"
      default: true
      streaming: true  # Enable streaming for this model
    - name: "gemini-2.5-flash"
      display_name: "Gemini 2.5 Flash"
      description: "Fast model for quick queries"
      streaming: true  # Enable streaming for this model

# Web interface configuration
web:
  title: "Jenkins Reader Agent"
  description: "Enterprise-grade Jenkins read-only agent"
  streaming: true  # Enable streaming in web interface
  stream_delay_ms: 50  # Delay between tokens for better UX
  stream_buffer_size: 1024  # Buffer size for streaming
```

### Multi-Agent Configuration

```yaml
# Multi-agent configuration
agents:
  - name: "jenkins_agent"
    display_name: "Jenkins Reader Agent"
    streaming: true  # Enable streaming for Jenkins agent

  - name: "github_agent"
    display_name: "GitHub Reader Agent"
    streaming: true  # Enable streaming for GitHub agent

# Global web interface configuration
web:
  title: "ADK Analyst - DevOps Analytics"
  streaming: true  # Enable streaming globally
  stream_delay_ms: 50
  stream_buffer_size: 1024
```

## 🎯 Usage Examples

### Web Interface

1. **Start the web server with streaming**:
   ```bash
   ENABLE_STREAMING=true python -m google.adk.cli web --port 8010
   ```

2. **Access the web interface**:
   - Open `http://localhost:8010`
   - Select an agent (Jenkins or GitHub)
   - Ask a question and watch the response stream in real-time

### CLI Usage

```bash
# Enable streaming for CLI
export ENABLE_STREAMING=true

# Run agent with streaming
python -m google.adk.cli run jenkins_agent
```

## 🔍 Testing Streaming

### Test Configuration

```bash
# Test streaming configuration
python -c "
import os
print('Streaming enabled:', os.getenv('ENABLE_STREAMING', 'false'))
print('Delay:', os.getenv('STREAM_DELAY_MS', '50'), 'ms')
print('Buffer size:', os.getenv('STREAM_BUFFER_SIZE', '1024'), 'bytes')
"
```

### Test ADK Version Compatibility

```bash
# Check ADK version (requires 1.2.0+)
python -c "
import google.adk
version = google.adk.__version__
major, minor, patch = map(int, version.split('.'))
streaming_supported = (major > 1) or (major == 1 and minor >= 2)
print(f'ADK Version: {version}')
print(f'Streaming supported: {streaming_supported}')
"
```

### Test Agent Imports

```bash
# Test that agents load correctly with streaming
python -c "
import jenkins_agent.agent
import github_agent.agent
print('✅ Both agents support streaming!')
"
```

## 🎛️ Performance Tuning

### Optimal Settings

**For Fast Responses (< 2 seconds)**:
```bash
STREAM_DELAY_MS=25
STREAM_CHUNK_SIZE=128
```

**For Long Responses (> 10 seconds)**:
```bash
STREAM_DELAY_MS=75
STREAM_CHUNK_SIZE=512
```

**For Real-time Dashboards**:
```bash
STREAM_DELAY_MS=10
STREAM_CHUNK_SIZE=64
```

### Network Considerations

- **Low Bandwidth**: Increase `STREAM_DELAY_MS` to 100-200ms
- **High Latency**: Increase `STREAM_BUFFER_SIZE` to 2048-4096 bytes
- **Mobile Networks**: Use conservative settings with longer delays

## 🔧 Troubleshooting

### Common Issues

#### Streaming Not Working
```
Issue: Responses appear all at once instead of streaming
Solution: Check ENABLE_STREAMING=true and ADK version >= 1.2.0
```

#### Slow Streaming
```
Issue: Streaming is too slow or choppy
Solution: Reduce STREAM_DELAY_MS or increase STREAM_CHUNK_SIZE
```

#### Buffer Overflows
```
Issue: Responses get cut off or corrupted
Solution: Increase STREAM_BUFFER_SIZE or reduce STREAM_CHUNK_SIZE
```

#### Agent Import Errors
```
Issue: Agents fail to load with streaming enabled
Solution: Check ADK version and configuration syntax
```

### Debug Commands

```bash
# Check streaming configuration
env | grep STREAM

# Test web server with debug logging
DEBUG=true ENABLE_STREAMING=true python -m google.adk.cli web --port 8010

# Verify agent configurations
python -c "
import yaml
with open('jenkins_agent/adk.yaml') as f:
    config = yaml.safe_load(f)
    print('Streaming enabled:', config.get('agent', {}).get('streaming', False))
"
```

## 📊 Monitoring

### Streaming Metrics

The platform automatically tracks streaming performance:

- **Total Streams**: Number of streaming responses
- **Average Chunks**: Average chunks per response
- **Average Delay**: Average delay between tokens
- **Total Bytes**: Total bytes streamed

### Performance Monitoring

```bash
# Monitor streaming performance
tail -f logs/adk-analyst.log | grep streaming
```

## 🔒 Security Considerations

### Rate Limiting

Streaming responses are subject to the same rate limiting as regular responses:

```bash
RATE_LIMIT_PER_MINUTE=100
MAX_CONCURRENT_REQUESTS=10
```

### Resource Usage

- Streaming uses slightly more memory due to buffering
- Network connections are held open longer
- Consider these factors when scaling

## 🚀 Best Practices

1. **Enable streaming for all user-facing interfaces**
2. **Use conservative delays (50-100ms) for better UX**
3. **Monitor performance and adjust settings based on usage**
4. **Test streaming with different network conditions**
5. **Provide fallback to non-streaming for compatibility**

## 📚 Additional Resources

- [Google ADK Streaming Documentation](https://google.github.io/adk-docs/get-started/streaming/quickstart-streaming/)
- [ADK Web Interface Guide](https://google.github.io/adk-docs/web-interface/)
- [Performance Optimization Guide](https://google.github.io/adk-docs/performance/)

---

**Streaming is now enabled for both Jenkins and GitHub agents in the ADK Analyst platform!** 🎉
