# GitHub Actions Deployment to Google Cloud Run

This document describes the automated deployment pipeline that deploys the ADK Analyst to Google Cloud Run using GitHub Actions.

## 🚀 Overview

The deployment pipeline automatically:
1. **Tests** the code on every push and pull request
2. **Builds** Docker images and pushes to GitHub Container Registry
3. **Deploys to Staging** on pushes to `main` or `develop` branches
4. **Deploys to Production** on pushes to `main` branch (after staging success)
5. **Creates Releases** when version tags are pushed

## 🏗️ Architecture

```
GitHub Repository (truxt-ai/adk-analyst)
├── Push to main/develop → GitHub Actions
├── Build Docker Image → GitHub Container Registry (ghcr.io)
├── Copy Image → Google Container Registry (gcr.io)
├── Deploy to Staging → Cloud Run (adk-analyst-staging)
├── Deploy to Production → Cloud Run (adk-analyst-jenkins-agent-production)
└── Create Release → GitHub Releases
```

## 🔧 Setup Requirements

### 1. Google Cloud Service Account

Create a service account with the following permissions:
- Cloud Run Admin
- Container Registry Service Agent
- Service Account User

```bash
# Create service account
gcloud iam service-accounts create adk-analyst-github-actions \
  --display-name="ADK Analyst GitHub Actions"

# Grant permissions
gcloud projects add-iam-policy-binding truxtsaas \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/run.admin"

gcloud projects add-iam-policy-binding truxtsaas \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/storage.admin"

gcloud projects add-iam-policy-binding truxtsaas \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/iam.serviceAccountUser"

# Create and download key
gcloud iam service-accounts keys create github-actions-key.json \
  --iam-account=<EMAIL>
```

### 2. GitHub Secrets

Add the following secrets to your GitHub repository:

1. **GCP_SERVICE_ACCOUNT_KEY**: Base64 encoded service account JSON key
   ```bash
   cat github-actions-key.json | base64 -w 0
   ```

### 3. Cloud Run Service Account

Ensure the Cloud Run service account exists:
```bash
gcloud iam service-accounts create adk-analyst-service \
  --display-name="ADK Analyst Service Account"

# Grant necessary permissions for the application
gcloud projects add-iam-policy-binding truxtsaas \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding truxtsaas \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/aiplatform.user"
```

## 🔄 Deployment Workflow

### Automatic Deployments

1. **Staging Deployment**:
   - Triggered on push to `main` or `develop` branches
   - Deploys to `adk-analyst-staging` service
   - Runs health checks
   - Smaller resource allocation for cost efficiency

2. **Production Deployment**:
   - Triggered on push to `main` branch (after staging success)
   - Deploys to `adk-analyst-jenkins-agent-production` service
   - Runs health checks
   - Full resource allocation for production workloads

3. **Release Creation**:
   - Triggered when version tags (v*) are pushed
   - Creates GitHub release with deployment information
   - Includes links to production service and documentation

### Manual Deployments

You can also trigger deployments manually using the GitHub Actions UI or by pushing to the appropriate branches.

## 🌐 Service URLs

After deployment, the services will be available at:

- **Staging**: `https://adk-analyst-staging-[hash]-uc.a.run.app`
- **Production**: `https://adk-analyst-jenkins-agent-production-[hash]-uc.a.run.app`

The exact URLs are displayed in the GitHub Actions logs and release notes.

## 📊 Monitoring

Monitor your deployments through:

1. **GitHub Actions**: View deployment logs and status
2. **Google Cloud Console**: 
   - [Cloud Run Services](https://console.cloud.google.com/run?project=truxtsaas)
   - [Production Metrics](https://console.cloud.google.com/run/detail/us-central1/adk-analyst-jenkins-agent-production/metrics?project=truxtsaas)
3. **Container Registry**: View pushed images

## 🔧 Configuration

### Environment Variables

The deployment sets the following environment variables:

**Staging**:
- `ENVIRONMENT=staging`
- `DEBUG=true`
- `LOG_LEVEL=DEBUG`
- Reduced resource limits

**Production**:
- `ENVIRONMENT=production`
- `DEBUG=false`
- `LOG_LEVEL=INFO`
- Full resource allocation

### Resource Allocation

**Staging**:
- CPU: 1 vCPU
- Memory: 2 GiB
- Min instances: 1
- Max instances: 5

**Production**:
- CPU: 2 vCPU
- Memory: 4 GiB
- Min instances: 2
- Max instances: 10

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify `GCP_SERVICE_ACCOUNT_KEY` secret is correctly set
   - Check service account permissions

2. **Deployment Failures**:
   - Check GitHub Actions logs for detailed error messages
   - Verify Cloud Run service account exists and has proper permissions

3. **Health Check Failures**:
   - Ensure the application exposes a `/health` endpoint
   - Check application logs in Cloud Run console

### Rollback

If a deployment fails, you can rollback using:

```bash
# List revisions
gcloud run revisions list --service=adk-analyst-jenkins-agent-production --region=us-central1

# Rollback to previous revision
gcloud run services update-traffic adk-analyst-jenkins-agent-production \
  --to-revisions=PREVIOUS_REVISION=100 \
  --region=us-central1
```

## 🔗 Related Documentation

- [Setup Guide](setup.md)
- [Development Guide](development.md)
- [Multi-Agent Architecture](../MULTI_AGENT_ARCHITECTURE.md)
- [Google Cloud Run Documentation](https://cloud.google.com/run/docs)
