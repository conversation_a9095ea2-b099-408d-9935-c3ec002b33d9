# Multi-Agent Project Structure

This document describes the new organized structure for the ADK Analyst multi-agent platform, designed for scalability and maintainability.

## 🎯 Overview

The ADK Analyst platform has been reorganized into a clean, scalable structure that separates concerns and makes it easy to add new agents while maintaining shared infrastructure.

## 📁 New Directory Structure

```
adk-analyst/
├── 🎯 agents/                     # Multi-Agent System
│   ├── jenkins/                   # Jenkins CI/CD Analysis Agent
│   │   ├── agent.py              # Main agent definition
│   │   ├── adk.yaml              # Agent-specific ADK config
│   │   ├── config/               # Agent configuration
│   │   │   ├── settings.py       # Settings and environment
│   │   │   └── schemas.py        # Data models
│   │   └── tools/                # Agent-specific tools
│   │       ├── connection_tools.py
│   │       ├── job_tools.py
│   │       └── build_tools.py
│   ├── github/                   # GitHub Repository Agent
│   │   ├── agent.py              # Main agent definition
│   │   ├── adk.yaml              # Agent-specific ADK config
│   │   ├── config/               # Agent configuration
│   │   └── tools/                # Agent-specific tools
│   │       ├── connection_tools.py
│   │       ├── repository_tools.py
│   │       ├── commit_tools.py
│   │       └── pr_tools.py
│   ├── dora_metrics/             # Future: DORA Metrics Agent
│   ├── jfrog/                    # Future: JFrog Agent
│   └── shared/                   # Agent-specific shared code
├── 🏗️ platform/                   # Platform Infrastructure
│   ├── shared_utils/             # Core utilities
│   │   ├── auth.py              # Authentication helpers
│   │   ├── logging.py           # Structured logging
│   │   ├── performance.py       # Caching & optimization
│   │   ├── security.py          # Security utilities
│   │   ├── streaming.py         # Streaming support
│   │   └── validation.py        # Input validation
│   ├── auth/                     # Authentication system
│   │   ├── authentication.py    # OAuth2/JWT handling
│   │   ├── authorization.py     # RBAC and permissions
│   │   ├── session_manager.py   # Session management
│   │   └── models.py           # Auth data models
│   ├── middleware/               # FastAPI middleware
│   │   ├── auth_middleware.py   # Authentication middleware
│   │   └── rate_limiting.py     # Rate limiting
│   └── validation/               # Input validation
│       ├── input_validators.py  # Request validation
│       └── schemas.py          # Validation schemas
├── 🚀 deployment/                 # Deployment Configurations
│   ├── docker/                   # Docker configurations
│   │   ├── Dockerfile.multi-agent # All agents in one container
│   │   ├── Dockerfile.jenkins   # Jenkins agent only
│   │   ├── Dockerfile.github    # GitHub agent only
│   │   └── Dockerfile.adk       # Legacy ADK container
│   ├── gcp/                      # Google Cloud configs
│   │   ├── cloud-run/           # Cloud Run configurations
│   │   │   ├── cloud-run.yaml   # Production config
│   │   │   └── cloud-run-staging.yaml # Staging config
│   │   └── cloudbuild/          # Cloud Build configs
│   │       └── cloudbuild.yaml  # CI/CD pipeline
│   └── github-actions/           # CI/CD workflows
│       └── ci-cd.yml            # GitHub Actions workflow
├── 📊 adk.yaml                    # Multi-agent configuration
├── 📋 requirements.txt            # Production dependencies
├── 🧪 tests/                      # Test suites
│   ├── agents/                   # Agent-specific tests
│   │   ├── jenkins/             # Jenkins agent tests
│   │   └── github/              # GitHub agent tests
│   ├── platform/                 # Platform tests
│   │   ├── shared_utils/        # Utility tests
│   │   ├── auth/                # Auth tests
│   │   └── middleware/          # Middleware tests
│   └── integration/              # Integration tests
├── 📚 docs/                       # Documentation
│   ├── MULTI_AGENT_STRUCTURE.md # This document
│   ├── GITHUB_ACTIONS_DEPLOYMENT.md # Deployment guide
│   └── setup.md                 # Setup instructions
└── 🔧 scripts/                    # Utility scripts
    ├── migrate-to-new-structure.sh # Migration script
    ├── setup-github-deployment.sh # Deployment setup
    └── validate-setup.sh         # Validation script
```

## 🔄 Migration from Old Structure

### Old Structure Issues
- **Scattered agents**: `jenkins_agent/` and `github_agent/` at root level
- **Mixed concerns**: Infrastructure and agents intermingled
- **Scaling challenges**: Adding new agents required root-level changes
- **Deployment complexity**: Multiple deployment configs scattered

### Migration Benefits
- **Organized agents**: All agents in `agents/` directory
- **Separated concerns**: Platform infrastructure in `platform/`
- **Scalable**: Easy to add new agents without affecting existing ones
- **Deployment clarity**: All deployment configs in `deployment/`

### Migration Process

1. **Automatic Migration**:
   ```bash
   ./scripts/migrate-to-new-structure.sh
   ```

2. **Manual Migration**:
   ```bash
   # Create new structure
   mkdir -p agents/{jenkins,github,shared} platform deployment/{docker,gcp,github-actions}
   
   # Move agents
   mv jenkins_agent/* agents/jenkins/
   mv github_agent/* agents/github/
   
   # Move platform components
   mv shared_utils platform/
   mv auth platform/
   mv middleware platform/
   mv validation platform/
   
   # Move deployment configs
   mv gcp-deployment/* deployment/gcp/
   mv .github/workflows/* deployment/github-actions/
   mv Dockerfile* deployment/docker/
   ```

## 🎯 Agent Development

### Adding a New Agent

1. **Create agent directory**:
   ```bash
   mkdir -p agents/my_new_agent/{config,tools}
   ```

2. **Create agent structure**:
   ```
   agents/my_new_agent/
   ├── __init__.py
   ├── agent.py              # Main agent definition
   ├── adk.yaml             # Agent-specific config
   ├── config/
   │   ├── __init__.py
   │   ├── settings.py      # Environment settings
   │   └── schemas.py       # Data models
   └── tools/
       ├── __init__.py
       └── my_tools.py      # Agent tools
   ```

3. **Update main configuration**:
   ```yaml
   # adk.yaml
   agents:
     - name: "my_new_agent"
       display_name: "My New Agent"
       description: "Description of the new agent"
       module: "agents.my_new_agent.agent"
       root_agent: "root_agent"
       config_file: "agents/my_new_agent/adk.yaml"
       streaming: true
   ```

### Agent Template

```python
# agents/my_new_agent/agent.py
from google.adk.agents import Agent
from platform.shared_utils.logging import get_logger

logger = get_logger(__name__)

agent_config = {
    'model': 'gemini-2.5-pro-preview-05-06',
    'name': 'my_new_agent',
    'instruction': 'Agent instructions here...',
    'tools': [
        # Add your tools here
    ]
}

root_agent = Agent(**agent_config)
```

## 🚀 Deployment Options

### 1. Multi-Agent Container (Recommended)
Deploy all agents in a single container:
```bash
docker build -f deployment/docker/Dockerfile.multi-agent -t adk-analyst:multi-agent .
```

### 2. Individual Agent Containers
Deploy specific agents separately:
```bash
# Jenkins only
docker build -f deployment/docker/Dockerfile.jenkins -t adk-analyst:jenkins .

# GitHub only
docker build -f deployment/docker/Dockerfile.github -t adk-analyst:github .
```

### 3. Cloud Run Deployment
Automatic deployment via GitHub Actions:
```bash
git push origin main  # Triggers automatic deployment
```

## 🧪 Testing

### Test Structure
```
tests/
├── agents/                   # Agent-specific tests
│   ├── jenkins/
│   │   ├── test_agent.py
│   │   └── test_tools.py
│   └── github/
│       ├── test_agent.py
│       └── test_tools.py
├── platform/                 # Platform tests
│   ├── shared_utils/
│   ├── auth/
│   └── middleware/
└── integration/              # Integration tests
    ├── test_multi_agent.py
    └── test_deployment.py
```

### Running Tests
```bash
# All tests
pytest tests/

# Agent-specific tests
pytest tests/agents/jenkins/
pytest tests/agents/github/

# Platform tests
pytest tests/platform/

# Integration tests
pytest tests/integration/
```

## 📚 Documentation

### Documentation Structure
- **Setup**: `docs/setup.md` - Initial setup instructions
- **Development**: `docs/development.md` - Development guidelines
- **Deployment**: `docs/GITHUB_ACTIONS_DEPLOYMENT.md` - Deployment guide
- **Architecture**: `MULTI_AGENT_ARCHITECTURE.md` - System architecture
- **Structure**: `docs/MULTI_AGENT_STRUCTURE.md` - This document

## 🔧 Development Workflow

1. **Setup development environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Work on specific agent**:
   ```bash
   cd agents/jenkins  # or agents/github
   # Make changes to agent code
   ```

3. **Test changes**:
   ```bash
   pytest tests/agents/jenkins/
   python -m google.adk.cli web --port 8010
   ```

4. **Deploy changes**:
   ```bash
   git add .
   git commit -m "Update Jenkins agent"
   git push origin main
   ```

## 🎉 Benefits of New Structure

### For Developers
- **Clear separation of concerns**
- **Easy to find and modify agent code**
- **Consistent structure across all agents**
- **Simplified testing and debugging**

### For Operations
- **Flexible deployment options**
- **Clear deployment configurations**
- **Easy to scale individual agents**
- **Simplified monitoring and maintenance**

### For Future Growth
- **Easy to add new agents**
- **Shared platform components**
- **Consistent patterns and practices**
- **Scalable architecture**

---

**Next Steps**: See [GitHub Actions Deployment Guide](GITHUB_ACTIONS_DEPLOYMENT.md) for automated deployment setup.
