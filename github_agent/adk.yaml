# ADK Configuration for GitHub Reader Agent
name: github_agent
display_name: "GitHub Reader Agent"
description: "Enterprise-grade GitHub read-only agent with comprehensive repository data extraction capabilities"
version: "1.0.0"

# Agent configuration
agent:
  module: "github_agent.agent"
  root_agent: "root_agent"
  streaming: true  # Enable streaming responses
  models:
    - name: "gemini-2.5-pro"
      display_name: "Gemini 2.5 Pro"
      description: "Advanced model for complex GitHub analysis"
      default: true
      streaming: true  # Enable streaming for this model
    - name: "gemini-2.5-flash"
      display_name: "Gemini 2.5 Flash"
      description: "Fast model for quick GitHub queries"
      streaming: true  # Enable streaming for this model

# Tools configuration
tools:
  - name: "validate_github_connection"
    description: "Test connectivity and retrieve GitHub user info"
    category: "connection"
  - name: "get_repository_info"
    description: "Get comprehensive repository information"
    category: "repository"
  - name: "get_repository_metrics"
    description: "Get repository metrics and statistics"
    category: "repository"
  - name: "get_commits_with_metadata"
    description: "Get commits with detailed metadata"
    category: "commits"
  - name: "get_commit_details"
    description: "Get detailed information about specific commits"
    category: "commits"
  - name: "get_pull_requests_data"
    description: "Get pull request data and analysis"
    category: "pull_requests"
  - name: "get_releases_and_tags"
    description: "Get releases and tags information"
    category: "releases"
  - name: "get_branch_analysis"
    description: "Analyze branch structure and patterns"
    category: "branches"

# Security configuration
security:
  read_only: true
  authentication_required: true
  audit_logging: true
  rate_limiting: true

# Environment configuration
environment:
  google_cloud_project: "${GOOGLE_CLOUD_PROJECT:-test-project}"
  github_credentials_secret: "${GITHUB_CREDENTIALS_SECRET:-github-credentials}"
  github_token: "${GITHUB_TOKEN}"
  github_personal_access_token: "${GITHUB_PERSONAL_ACCESS_TOKEN}"
  debug: "${DEBUG:-false}"

# Web interface configuration
web:
  title: "GitHub Reader Agent"
  description: "Enterprise-grade GitHub read-only agent"
  favicon: "/adk_favicon.svg"
  theme: "default"
  streaming: true  # Enable streaming in web interface
  stream_delay_ms: 50  # Delay between tokens for better UX

# Capabilities
capabilities:
  - "GitHub repository data extraction"
  - "Commit history analysis"
  - "Pull request analysis"
  - "Branch pattern analysis"
  - "Release and tag management"
  - "Repository metrics and statistics"
  - "Performance monitoring"
  - "Security auditing"

# Enterprise features
enterprise_features:
  - "Google Cloud IAM integration"
  - "Structured logging and monitoring"
  - "Auto-scaling and high availability"
  - "Compliance and audit trails"
  - "Performance optimization"
  - "Error handling and recovery"
