"""
Pytest configuration and fixtures for ADK Analyst tests.
"""

import asyncio
from typing import Any, Async<PERSON>enerator, Dict
from unittest.mock import AsyncMock, Mock

import pytest


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_context():
    """Mock tool context for testing."""
    context = Mock()
    context.auth_context = None
    context.user_id = "test-user"
    context.session_id = "test-session"
    return context


@pytest.fixture
def jenkins_config():
    """Jenkins test configuration."""
    return {
        "url": "https://test-jenkins.example.com",
        "username": "test-user",
        "password": "test-password",
        "timeout": 30,
    }


@pytest.fixture
def github_config():
    """GitHub test configuration."""
    return {
        "token": "test-github-token",
        "base_url": "https://api.github.com",
        "timeout": 30,
    }


@pytest.fixture
def sample_jenkins_job():
    """Sample Jenkins job data for testing."""
    return {
        "name": "test-job",
        "url": "https://test-jenkins.example.com/job/test-job/",
        "color": "blue",
        "buildable": True,
        "builds": [
            {"number": 1, "url": "https://test-jenkins.example.com/job/test-job/1/"}
        ],
    }


@pytest.fixture
def sample_github_repo():
    """Sample GitHub repository data for testing."""
    return {
        "id": 123456789,
        "name": "test-repo",
        "full_name": "test-org/test-repo",
        "private": False,
        "html_url": "https://github.com/test-org/test-repo",
        "description": "Test repository",
        "stargazers_count": 10,
        "forks_count": 5,
        "language": "Python",
        "default_branch": "main",
    }


@pytest.fixture
async def mock_jenkins_client():
    """Mock Jenkins client for testing."""
    client = AsyncMock()
    client.get_info.return_value = {"version": "2.401.3", "mode": "NORMAL"}
    client.get_jobs.return_value = []
    return client


@pytest.fixture
async def mock_github_client():
    """Mock GitHub client for testing."""
    client = AsyncMock()
    client.get_user.return_value = Mock(login="test-user")
    client.get_repo.return_value = Mock()
    return client


# Pytest markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "security: Security tests")
    config.addinivalue_line("markers", "slow: Slow running tests")


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file paths."""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "security" in str(item.fspath):
            item.add_marker(pytest.mark.security)

        # Mark slow tests
        if "slow" in item.name or "integration" in str(item.fspath):
            item.add_marker(pytest.mark.slow)
