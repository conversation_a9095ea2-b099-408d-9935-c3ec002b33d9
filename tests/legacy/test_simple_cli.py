#!/usr/bin/env python3
"""Simple test script for Jenkins Reader Agent."""

import asyncio
import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.jenkins.agent import root_agent


async def simple_test():
    """Simple test of agent functionality."""
    print("🚀 Simple Jenkins Agent Test")
    print("=" * 40)
    
    print(f"Agent Name: {root_agent.name}")
    print(f"Agent Model: {root_agent.model}")
    print(f"Number of Tools: {len(root_agent.tools)}")
    
    # List available tools
    print("\nAvailable Tools:")
    for tool in root_agent.tools:
        print(f"  - {tool.__name__}")
    
    print("\n✅ Basic agent configuration test passed!")


def main():
    """Main function."""
    try:
        asyncio.run(simple_test())
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
