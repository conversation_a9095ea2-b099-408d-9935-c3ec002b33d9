#!/usr/bin/env python3
"""Test script for Jenkins Reader Agent CLI functionality."""

import asyncio
import os
import sys
from typing import Dict, Any

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from jenkins_agent.agent import root_agent, get_agent_info, get_available_models


async def test_agent_basic_functionality():
    """Test basic agent functionality."""
    print("🚀 Testing Jenkins Reader Agent CLI Functionality")
    print("=" * 60)
    
    # Test 1: Agent Info
    print("\n📋 1. Agent Information:")
    agent_info = get_agent_info()
    print(f"   Name: {agent_info['name']}")
    print(f"   Version: {agent_info['version']}")
    print(f"   Description: {agent_info['description']}")
    print(f"   Available Tools: {len(agent_info['tools'])}")
    
    # Test 2: Available Models
    print("\n🤖 2. Available Models:")
    models = get_available_models()
    for model in models['models']:
        print(f"   - {model['name']}: {model['description']}")
    
    # Test 3: Agent Configuration
    print("\n⚙️  3. Agent Configuration:")
    print(f"   Model: {root_agent.model}")
    print(f"   Tools: {len(root_agent.tools)}")
    print(f"   Has Security Callback: {root_agent.before_agent_callback is not None}")
    print(f"   Has Audit Callback: {root_agent.after_agent_callback is not None}")
    
    # Test 4: Simple Query (without actual Jenkins connection)
    print("\n💬 4. Testing Agent Response (Mock Query):")
    try:
        # Test with a simple informational query
        response_parts = []
        async for chunk in root_agent.run_async("What are your capabilities as a Jenkins agent?"):
            response_parts.append(str(chunk))
        response = "".join(response_parts)
        print(f"   Response: {response[:200]}..." if len(response) > 200 else f"   Response: {response}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n✅ CLI Testing Complete!")


async def test_interactive_mode():
    """Test interactive mode simulation."""
    print("\n🎯 Interactive Mode Simulation")
    print("=" * 40)
    
    test_queries = [
        "What tools do you have available?",
        "How can you help with Jenkins analysis?",
        "What security measures do you implement?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: {query}")
        try:
            response_parts = []
            async for chunk in root_agent.run_async(query):
                response_parts.append(str(chunk))
            response = "".join(response_parts)
            # Truncate long responses for readability
            if len(response) > 300:
                print(f"   Response: {response[:300]}...")
            else:
                print(f"   Response: {response}")
        except Exception as e:
            print(f"   Error: {e}")


def main():
    """Main test function."""
    print("Jenkins Reader Agent - CLI Testing Suite")
    print("========================================")
    
    # Check environment
    print("\n🔧 Environment Check:")
    print(f"   Python Version: {sys.version}")
    print(f"   Working Directory: {os.getcwd()}")
    print(f"   Google Credentials: {'Set' if os.getenv('GOOGLE_APPLICATION_CREDENTIALS') else 'Not Set'}")
    
    # Run tests
    try:
        asyncio.run(test_agent_basic_functionality())
        asyncio.run(test_interactive_mode())
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
