"""
ADK Analyst Platform Package

This package contains the core platform infrastructure:
- Shared utilities for all agents
- Authentication and authorization
- Middleware components
- Validation and security

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "<PERSON><PERSON><PERSON>"

# Import core platform components
try:
    from . import shared_utils
    from . import auth
    from . import middleware
    from . import validation
    
    __all__ = [
        "shared_utils",
        "auth", 
        "middleware",
        "validation"
    ]
    
except ImportError as e:
    # Handle import errors gracefully during development
    print(f"Warning: Could not import all platform components: {e}")
    __all__ = []
