"""
Authentication and Authorization Module

This module provides enterprise-grade authentication and authorization
capabilities for the Jenkins Reader Agent, including:

- OAuth2/OIDC integration with Google Cloud
- JWT token management
- Role-based access control (RBAC)
- Session management
- Security middleware
- Multi-agent authentication

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from .authentication import (
    AuthenticationService,
    OAuth2Provider,
    JWTManager,
    TokenValidator,
)
from .authorization import (
    AuthorizationService,
    RoleManager,
    PermissionChecker,
    Role,
    Permission,
)
from .session_manager import (
    SessionManager,
    SecureSession,
    SessionStore,
)
from .models import (
    User,
    AuthToken,
    RefreshToken,
    UserRole,
    AuthRequest,
    AuthResponse,
    AgentType,
    RoleType,
    PermissionType,
    AgentRegistration,
    AgentCommunication,
)
from .agent_manager import (
    AgentAuthenticationService,
    AgentCommunicationService,
    AgentOrchestrationService,
)
from .exceptions import (
    AuthenticationError,
    AuthorizationError,
    TokenExpiredError,
    InvalidTokenError,
    InsufficientPer<PERSON>s<PERSON>rror,
    SessionExpiredError,
)

__all__ = [
    # Authentication
    "AuthenticationService",
    "OAuth2Provider",
    "JWTManager",
    "TokenValidator",

    # Authorization
    "AuthorizationService",
    "RoleManager",
    "PermissionChecker",
    "Role",
    "Permission",

    # Session Management
    "SessionManager",
    "SecureSession",
    "SessionStore",

    # Agent Management
    "AgentAuthenticationService",
    "AgentCommunicationService",
    "AgentOrchestrationService",

    # Models
    "User",
    "AuthToken",
    "RefreshToken",
    "UserRole",
    "AuthRequest",
    "AuthResponse",
    "AgentType",
    "RoleType",
    "PermissionType",
    "AgentRegistration",
    "AgentCommunication",

    # Exceptions
    "AuthenticationError",
    "AuthorizationError",
    "TokenExpiredError",
    "InvalidTokenError",
    "InsufficientPermissionsError",
    "SessionExpiredError",
]

# Version information
__version__ = "1.0.0"
__author__ = "Jenkins Reader Agent Team"
