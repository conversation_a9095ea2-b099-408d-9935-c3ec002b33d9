"""
Authentication and Authorization Exceptions

This module defines custom exceptions for authentication and authorization
operations in the Jenkins Reader Agent multi-agent system.

Author: <PERSON><PERSON><PERSON>
Version: 1.0.0
"""

from typing import Optional, Dict, Any
from fastapi import status


class AuthenticationError(Exception):
    """Base exception for authentication errors."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        status_code: int = status.HTTP_401_UNAUTHORIZED,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class AuthorizationError(Exception):
    """Base exception for authorization errors."""
    
    def __init__(
        self,
        message: str = "Authorization failed",
        status_code: int = status.HTTP_403_FORBIDDEN,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class TokenExpiredError(AuthenticationError):
    """Raised when a token has expired."""
    
    def __init__(
        self,
        message: str = "Token has expired",
        token_type: str = "access_token"
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details={"token_type": token_type}
        )


class InvalidTokenError(AuthenticationError):
    """Raised when a token is invalid or malformed."""
    
    def __init__(
        self,
        message: str = "Invalid token",
        token_type: str = "access_token"
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details={"token_type": token_type}
        )


class InsufficientPermissionsError(AuthorizationError):
    """Raised when user lacks required permissions."""
    
    def __init__(
        self,
        message: str = "Insufficient permissions",
        required_permission: Optional[str] = None,
        user_role: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            details={
                "required_permission": required_permission,
                "user_role": user_role
            }
        )


class SessionExpiredError(AuthenticationError):
    """Raised when a session has expired."""
    
    def __init__(
        self,
        message: str = "Session has expired",
        session_id: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details={"session_id": session_id}
        )


class OAuth2Error(AuthenticationError):
    """Raised for OAuth2-specific errors."""
    
    def __init__(
        self,
        message: str = "OAuth2 authentication failed",
        error_code: Optional[str] = None,
        error_description: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details={
                "error_code": error_code,
                "error_description": error_description
            }
        )


class RoleNotFoundError(AuthorizationError):
    """Raised when a role is not found."""
    
    def __init__(
        self,
        message: str = "Role not found",
        role_name: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details={"role_name": role_name}
        )


class PermissionNotFoundError(AuthorizationError):
    """Raised when a permission is not found."""
    
    def __init__(
        self,
        message: str = "Permission not found",
        permission_name: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details={"permission_name": permission_name}
        )


class UserNotFoundError(AuthenticationError):
    """Raised when a user is not found."""
    
    def __init__(
        self,
        message: str = "User not found",
        user_id: Optional[str] = None,
        email: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details={
                "user_id": user_id,
                "email": email
            }
        )


class SecurityViolationError(AuthenticationError):
    """Raised for security violations."""
    
    def __init__(
        self,
        message: str = "Security violation detected",
        violation_type: Optional[str] = None,
        client_ip: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            details={
                "violation_type": violation_type,
                "client_ip": client_ip
            }
        )


class RateLimitExceededError(AuthenticationError):
    """Raised when rate limit is exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        limit: Optional[int] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details={
                "retry_after": retry_after,
                "limit": limit
            }
        )


class ConfigurationError(Exception):
    """Raised for authentication configuration errors."""
    
    def __init__(
        self,
        message: str = "Authentication configuration error",
        config_key: Optional[str] = None
    ):
        self.message = message
        self.config_key = config_key
        super().__init__(self.message)


class AgentRegistrationError(AuthenticationError):
    """Raised for agent registration errors."""
    
    def __init__(
        self,
        message: str = "Agent registration failed",
        agent_type: Optional[str] = None,
        agent_name: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            details={
                "agent_type": agent_type,
                "agent_name": agent_name
            }
        )


class AgentAuthenticationError(AuthenticationError):
    """Raised for agent authentication errors."""
    
    def __init__(
        self,
        message: str = "Agent authentication failed",
        agent_id: Optional[str] = None,
        api_key_preview: Optional[str] = None
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details={
                "agent_id": agent_id,
                "api_key_preview": api_key_preview
            }
        )


class AgentCommunicationError(Exception):
    """Raised for inter-agent communication errors."""
    
    def __init__(
        self,
        message: str = "Agent communication failed",
        source_agent: Optional[str] = None,
        target_agent: Optional[str] = None,
        communication_id: Optional[str] = None
    ):
        self.message = message
        self.source_agent = source_agent
        self.target_agent = target_agent
        self.communication_id = communication_id
        super().__init__(self.message)
