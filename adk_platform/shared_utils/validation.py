"""Shared validation utilities for enterprise agents."""

import re
import urllib.parse
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from .exceptions import AuthorizationError, ValidationError, SecurityError
from .logging import get_logger, log_security_event
from .auth import get_user_identity_from_context

logger = get_logger(__name__)


async def validate_request_permissions(
    tool_context: ToolContext,
    required_permission: str
) -> bool:
    """
    Validate that the user has the required permissions for the operation.
    
    Args:
        tool_context: ADK tool context with user authentication
        required_permission: Required permission (e.g., "repos.read", "commits.read")
        
    Returns:
        bool: True if user has permission
        
    Raises:
        AuthorizationError: If user lacks required permissions
    """
    logger.debug(
        "Validating request permissions",
        extra={
            "required_permission": required_permission
        }
    )
    
    # Extract user identity from tool context
    user_identity = await get_user_identity_from_context(tool_context)

    # In ADK environment, tool_context might be None or have different structure
    # Allow requests to proceed if we're in a testing/development environment
    if not user_identity and tool_context is not None:
        # Try to create a default user identity for ADK environment
        try:
            # Check if we're in ADK environment by looking for specific attributes
            if hasattr(tool_context, 'session_id') or hasattr(tool_context, 'invocation_id'):
                # Create a default user identity for ADK environment
                user_identity = type('MockUser', (), {'user_id': 'adk_user'})()
                logger.debug("Created default user identity for ADK environment")
            else:
                await log_security_event(
                    event_type="authentication_missing",
                    severity="MEDIUM",  # Reduced severity for development
                    description="No user identity found in request context",
                    required_permission=required_permission
                )
                # Don't raise error in development - just log and continue
                logger.warning(f"No user identity found, proceeding with permission check for {required_permission}")
        except Exception as e:
            logger.debug(f"Failed to create default user identity: {e}")

    # If still no user identity, allow in development mode
    if not user_identity:
        logger.info(f"Proceeding without user identity validation for permission: {required_permission}")
        return True
    
    # Define permission hierarchy
    permission_hierarchy = {
        # Repository permissions
        "repos.read": ["admin", "maintainer", "write", "read"],
        "repos.write": ["admin", "maintainer", "write"],
        "repos.admin": ["admin"],
        
        # Commit permissions
        "commits.read": ["admin", "maintainer", "write", "read"],
        "commits.write": ["admin", "maintainer", "write"],
        
        # Pull request permissions
        "pulls.read": ["admin", "maintainer", "write", "read"],
        "pulls.write": ["admin", "maintainer", "write"],
        
        # Release permissions
        "releases.read": ["admin", "maintainer", "write", "read"],
        "releases.write": ["admin", "maintainer", "write"],
        
        # Workflow permissions
        "workflows.read": ["admin", "maintainer", "write", "read"],
        "workflows.write": ["admin", "maintainer", "write"],
        
        # Organization permissions
        "orgs.read": ["admin", "maintainer", "write", "read"],
        "orgs.write": ["admin", "maintainer"],
        
        # General permissions
        "read": ["admin", "maintainer", "write", "read"],
        "write": ["admin", "maintainer", "write"],
        "admin": ["admin"]
    }
    
    # In production, implement actual permission checking
    # This would typically involve checking user roles against IAM or RBAC
    allowed_roles = permission_hierarchy.get(required_permission, [])
    
    # TODO: Replace with actual role checking
    # user_roles = await get_user_roles(user_identity)
    # has_permission = any(role in allowed_roles for role in user_roles)
    
    # For now, grant permission to all authenticated users
    has_permission = True
    
    if not has_permission:
        await log_security_event(
            event_type="permission_denied",
            user=user_identity,
            severity="WARNING",
            required_permission=required_permission,
            allowed_roles=allowed_roles
        )
        raise AuthorizationError(
            f"User lacks required permission: {required_permission}",
            required_permission=required_permission
        )
    
    logger.debug(
        "Permission validation successful",
        extra={
            "user_id": getattr(user_identity, 'user_id', 'unknown'),
            "required_permission": required_permission
        }
    )
    
    return True


def sanitize_input(
    value: Any,
    max_length: Optional[int] = None,
    allowed_chars: Optional[str] = None,
    strip_html: bool = True
) -> str:
    """
    Sanitize user input to prevent injection attacks.
    
    Args:
        value: Input value to sanitize
        max_length: Maximum allowed length
        allowed_chars: Regex pattern for allowed characters
        strip_html: Whether to strip HTML tags
        
    Returns:
        str: Sanitized input string
        
    Raises:
        ValidationError: If input fails validation
    """
    if value is None:
        return ""
    
    # Convert to string
    sanitized = str(value)
    
    # Strip HTML tags if requested
    if strip_html:
        sanitized = re.sub(r'<[^>]+>', '', sanitized)
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\';\\]', '', sanitized)
    
    # Check length
    if max_length and len(sanitized) > max_length:
        raise ValidationError(
            f"Input too long (max {max_length} characters)",
            field="input",
            value=sanitized[:50] + "..." if len(sanitized) > 50 else sanitized
        )
    
    # Check allowed characters
    if allowed_chars and not re.match(allowed_chars, sanitized):
        raise ValidationError(
            "Input contains invalid characters",
            field="input",
            value=sanitized[:50] + "..." if len(sanitized) > 50 else sanitized
        )
    
    return sanitized


def validate_url(url: str, allowed_schemes: List[str] = None) -> bool:
    """
    Validate URL format and scheme.
    
    Args:
        url: URL to validate
        allowed_schemes: List of allowed URL schemes (default: ['http', 'https'])
        
    Returns:
        bool: True if URL is valid
        
    Raises:
        ValidationError: If URL is invalid
    """
    if not url:
        raise ValidationError("URL is required", field="url", value=url)
    
    if allowed_schemes is None:
        allowed_schemes = ['http', 'https']
    
    try:
        parsed = urllib.parse.urlparse(url)
        
        if not parsed.scheme:
            raise ValidationError("URL must include scheme", field="url", value=url)
        
        if parsed.scheme not in allowed_schemes:
            raise ValidationError(
                f"URL scheme must be one of: {', '.join(allowed_schemes)}",
                field="url",
                value=url
            )
        
        if not parsed.netloc:
            raise ValidationError("URL must include hostname", field="url", value=url)
        
        return True
        
    except urllib.parse.ParseError as e:
        raise ValidationError(f"Invalid URL format: {str(e)}", field="url", value=url)


def validate_repository_name(repo_name: str) -> bool:
    """
    Validate GitHub repository name format.
    
    Args:
        repo_name: Repository name in format "owner/repo"
        
    Returns:
        bool: True if repository name is valid
        
    Raises:
        ValidationError: If repository name is invalid
    """
    if not repo_name:
        raise ValidationError("Repository name is required", field="repo_name", value=repo_name)
    
    # Check format: owner/repo
    if '/' not in repo_name:
        raise ValidationError(
            "Repository name must be in format 'owner/repo'",
            field="repo_name",
            value=repo_name
        )
    
    parts = repo_name.split('/')
    if len(parts) != 2:
        raise ValidationError(
            "Repository name must be in format 'owner/repo'",
            field="repo_name",
            value=repo_name
        )
    
    owner, repo = parts
    
    # Validate owner name
    if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-])*[a-zA-Z0-9]$|^[a-zA-Z0-9]$', owner):
        raise ValidationError(
            "Invalid owner name format",
            field="owner",
            value=owner
        )
    
    # Validate repository name
    if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-_\.])*[a-zA-Z0-9]$|^[a-zA-Z0-9]$', repo):
        raise ValidationError(
            "Invalid repository name format",
            field="repo",
            value=repo
        )
    
    return True


def validate_commit_hash(commit_hash: str) -> bool:
    """
    Validate Git commit hash format.
    
    Args:
        commit_hash: Git commit hash
        
    Returns:
        bool: True if commit hash is valid
        
    Raises:
        ValidationError: If commit hash is invalid
    """
    if not commit_hash:
        raise ValidationError("Commit hash is required", field="commit_hash", value=commit_hash)
    
    # Check length (7-40 characters for Git hashes)
    if len(commit_hash) < 7 or len(commit_hash) > 40:
        raise ValidationError(
            "Commit hash must be 7-40 characters long",
            field="commit_hash",
            value=commit_hash
        )
    
    # Check format (hexadecimal)
    if not re.match(r'^[a-fA-F0-9]+$', commit_hash):
        raise ValidationError(
            "Commit hash must contain only hexadecimal characters",
            field="commit_hash",
            value=commit_hash
        )
    
    return True


def validate_branch_name(branch_name: str) -> bool:
    """
    Validate Git branch name format.
    
    Args:
        branch_name: Git branch name
        
    Returns:
        bool: True if branch name is valid
        
    Raises:
        ValidationError: If branch name is invalid
    """
    if not branch_name:
        raise ValidationError("Branch name is required", field="branch_name", value=branch_name)
    
    # Check length
    if len(branch_name) > 255:
        raise ValidationError(
            "Branch name too long (max 255 characters)",
            field="branch_name",
            value=branch_name
        )
    
    # Check for invalid characters and patterns
    invalid_patterns = [
        r'\.\.', r'@{', r'\\', r'\s+$', r'^\s+', r'/$', r'^/', r'//', r'\.$'
    ]
    
    for pattern in invalid_patterns:
        if re.search(pattern, branch_name):
            raise ValidationError(
                "Branch name contains invalid characters or patterns",
                field="branch_name",
                value=branch_name
            )
    
    # Check for control characters
    if any(ord(c) < 32 or ord(c) == 127 for c in branch_name):
        raise ValidationError(
            "Branch name contains control characters",
            field="branch_name",
            value=branch_name
        )
    
    return True


def validate_pagination_params(page: int = 1, per_page: int = 30, max_per_page: int = 100) -> tuple:
    """
    Validate pagination parameters.
    
    Args:
        page: Page number
        per_page: Items per page
        max_per_page: Maximum items per page
        
    Returns:
        tuple: Validated (page, per_page) values
        
    Raises:
        ValidationError: If pagination parameters are invalid
    """
    if page < 1:
        raise ValidationError("Page number must be >= 1", field="page", value=page)
    
    if per_page < 1:
        raise ValidationError("Per page must be >= 1", field="per_page", value=per_page)
    
    if per_page > max_per_page:
        raise ValidationError(
            f"Per page must be <= {max_per_page}",
            field="per_page",
            value=per_page
        )
    
    return page, per_page


def validate_input_parameters(params: Dict[str, Any], allowed_params: List[str]) -> Dict[str, Any]:
    """
    Validate input parameters against allowed list.

    Args:
        params: Input parameters to validate
        allowed_params: List of allowed parameter names

    Returns:
        Dict: Validated parameters

    Raises:
        ValidationError: If invalid parameters are found
    """
    validated = {}

    for key, value in params.items():
        if key not in allowed_params:
            raise ValidationError(
                f"Parameter '{key}' is not allowed",
                field="parameters",
                value=key
            )
        validated[key] = value

    return validated


def sanitize_job_config(config_xml: str) -> Dict[str, Any]:
    """
    Sanitize Jenkins job configuration XML.

    Args:
        config_xml: Raw job configuration XML

    Returns:
        Dict: Sanitized configuration data
    """
    import xml.etree.ElementTree as ET

    try:
        # Parse XML
        root = ET.fromstring(config_xml)

        # Extract key configuration elements
        config_data = {
            "job_type": root.tag,
            "description": "",
            "disabled": False,
            "scm": {},
            "triggers": [],
            "builders": [],
            "publishers": [],
            "properties": []
        }

        # Extract description
        desc_elem = root.find("description")
        if desc_elem is not None and desc_elem.text:
            config_data["description"] = desc_elem.text.strip()

        # Extract disabled status
        disabled_elem = root.find("disabled")
        if disabled_elem is not None and disabled_elem.text:
            config_data["disabled"] = disabled_elem.text.lower() == "true"

        # Extract SCM information
        scm_elem = root.find("scm")
        if scm_elem is not None:
            config_data["scm"] = {
                "class": scm_elem.get("class", ""),
                "plugin": scm_elem.get("plugin", "")
            }

            # Extract Git/SVN specific info (sanitized)
            for url_elem in scm_elem.findall(".//url"):
                if url_elem.text:
                    # Sanitize URL to remove credentials
                    sanitized_url = re.sub(r'://[^@]+@', '://***:***@', url_elem.text)
                    config_data["scm"]["url"] = sanitized_url
                    break

        # Extract triggers
        triggers_elem = root.find("triggers")
        if triggers_elem is not None:
            for trigger in triggers_elem:
                config_data["triggers"].append({
                    "class": trigger.get("class", ""),
                    "plugin": trigger.get("plugin", "")
                })

        # Extract builders
        builders_elem = root.find("builders")
        if builders_elem is not None:
            for builder in builders_elem:
                config_data["builders"].append({
                    "class": builder.get("class", ""),
                    "plugin": builder.get("plugin", "")
                })

        # Extract publishers
        publishers_elem = root.find("publishers")
        if publishers_elem is not None:
            for publisher in publishers_elem:
                config_data["publishers"].append({
                    "class": publisher.get("class", ""),
                    "plugin": publisher.get("plugin", "")
                })

        # Extract properties
        properties_elem = root.find("properties")
        if properties_elem is not None:
            for prop in properties_elem:
                config_data["properties"].append({
                    "class": prop.get("class", ""),
                    "plugin": prop.get("plugin", "")
                })

        return config_data

    except ET.ParseError as e:
        logger.warning(f"Failed to parse job configuration XML: {e}")
        return {
            "job_type": "unknown",
            "description": "Failed to parse configuration",
            "error": str(e),
            "raw_size": len(config_xml)
        }
    except Exception as e:
        logger.error(f"Error sanitizing job configuration: {e}")
        return {
            "job_type": "unknown",
            "description": "Error processing configuration",
            "error": str(e),
            "raw_size": len(config_xml)
        }
