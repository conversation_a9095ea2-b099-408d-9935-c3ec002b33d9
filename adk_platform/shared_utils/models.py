"""Shared data models for enterprise agents."""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field


class ErrorInfo(BaseModel):
    """Error information model."""
    code: str = Field(description="Error code")
    message: str = Field(description="Error message")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional error details")


class ResponseMetadata(BaseModel):
    """Response metadata model."""
    timestamp: str = Field(description="Response timestamp in ISO format")
    service_url: Optional[str] = Field(default=None, description="Service URL")
    total_records: int = Field(default=0, description="Total number of records")
    query_duration_ms: int = Field(default=0, description="Query duration in milliseconds")
    api_version: Optional[str] = Field(default=None, description="API version used")
    rate_limit_remaining: Optional[int] = Field(default=None, description="Remaining API calls")
    rate_limit_reset: Optional[int] = Field(default=None, description="Rate limit reset timestamp")


class BaseResponse(BaseModel):
    """Base response model for all agent operations."""
    status: str = Field(description="Response status: success, error, or warning")
    data: Dict[str, Any] = Field(default_factory=dict, description="Response data")
    metadata: ResponseMetadata = Field(description="Response metadata")
    errors: List[ErrorInfo] = Field(default_factory=list, description="List of errors")
    warnings: List[str] = Field(default_factory=list, description="List of warnings")


class UserIdentity(BaseModel):
    """User identity model."""
    user_id: str = Field(description="Unique user identifier")
    email: Optional[str] = Field(default=None, description="User email")
    name: Optional[str] = Field(default=None, description="User display name")
    roles: List[str] = Field(default_factory=list, description="User roles")
    permissions: List[str] = Field(default_factory=list, description="User permissions")


class AuthContext(BaseModel):
    """Authentication context model."""
    user_identity: UserIdentity = Field(description="User identity information")
    session_id: Optional[str] = Field(default=None, description="Session identifier")
    token_type: Optional[str] = Field(default=None, description="Token type")
    expires_at: Optional[datetime] = Field(default=None, description="Token expiration time")


class ServiceCredentials(BaseModel):
    """Base service credentials model."""
    service_name: str = Field(description="Service name")
    token: str = Field(description="Authentication token")
    url: Optional[str] = Field(default=None, description="Service URL")
    username: Optional[str] = Field(default=None, description="Username")
    additional_headers: Dict[str, str] = Field(default_factory=dict, description="Additional headers")


class GitHubCredentials(ServiceCredentials):
    """GitHub-specific credentials model."""
    service_name: str = Field(default="github", description="Service name")
    api_version: str = Field(default="2022-11-28", description="GitHub API version")
    
    class Config:
        extra = "forbid"


class JenkinsCredentials(ServiceCredentials):
    """Jenkins-specific credentials model."""
    service_name: str = Field(default="jenkins", description="Service name")
    token: Optional[str] = Field(default=None, description="Jenkins API token (alias for api_token)")
    url: str = Field(description="Jenkins server URL")
    username: str = Field(description="Jenkins username")
    password: Optional[str] = Field(default=None, description="Jenkins password")
    api_token: Optional[str] = Field(default=None, description="Jenkins API token")

    class Config:
        extra = "forbid"


class JFrogCredentials(ServiceCredentials):
    """JFrog-specific credentials model."""
    service_name: str = Field(default="jfrog", description="Service name")
    password: Optional[str] = Field(default=None, description="Password")
    
    class Config:
        extra = "forbid"


class RateLimitInfo(BaseModel):
    """Rate limit information model."""
    limit: int = Field(description="Rate limit maximum")
    remaining: int = Field(description="Remaining requests")
    reset_time: int = Field(description="Reset timestamp")
    retry_after: Optional[int] = Field(default=None, description="Retry after seconds")


class CacheEntry(BaseModel):
    """Cache entry model."""
    key: str = Field(description="Cache key")
    value: Any = Field(description="Cached value")
    created_at: datetime = Field(description="Creation timestamp")
    expires_at: Optional[datetime] = Field(default=None, description="Expiration timestamp")
    access_count: int = Field(default=0, description="Access count")
    last_accessed: Optional[datetime] = Field(default=None, description="Last access timestamp")


class AuditEvent(BaseModel):
    """Audit event model."""
    event_type: str = Field(description="Event type")
    timestamp: datetime = Field(description="Event timestamp")
    user_id: Optional[str] = Field(default=None, description="User identifier")
    resource: Optional[str] = Field(default=None, description="Resource accessed")
    action: Optional[str] = Field(default=None, description="Action performed")
    result: str = Field(description="Event result: success, failure, warning")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional event details")
    ip_address: Optional[str] = Field(default=None, description="Client IP address")
    user_agent: Optional[str] = Field(default=None, description="Client user agent")


class PerformanceMetrics(BaseModel):
    """Performance metrics model."""
    operation: str = Field(description="Operation name")
    duration_ms: int = Field(description="Operation duration in milliseconds")
    timestamp: datetime = Field(description="Metrics timestamp")
    success: bool = Field(description="Operation success status")
    error_type: Optional[str] = Field(default=None, description="Error type if failed")
    resource_usage: Dict[str, Any] = Field(default_factory=dict, description="Resource usage metrics")


class SecurityEvent(BaseModel):
    """Security event model."""
    event_type: str = Field(description="Security event type")
    severity: str = Field(description="Event severity: LOW, MEDIUM, HIGH, CRITICAL")
    timestamp: datetime = Field(description="Event timestamp")
    user_id: Optional[str] = Field(default=None, description="User identifier")
    source_ip: Optional[str] = Field(default=None, description="Source IP address")
    description: str = Field(description="Event description")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional event details")
    action_taken: Optional[str] = Field(default=None, description="Action taken in response")


class ValidationRule(BaseModel):
    """Input validation rule model."""
    field: str = Field(description="Field name")
    rule_type: str = Field(description="Validation rule type")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Rule parameters")
    error_message: str = Field(description="Error message for validation failure")
    required: bool = Field(default=True, description="Whether field is required")


class ConfigurationSetting(BaseModel):
    """Configuration setting model."""
    key: str = Field(description="Configuration key")
    value: Any = Field(description="Configuration value")
    description: Optional[str] = Field(default=None, description="Setting description")
    category: Optional[str] = Field(default=None, description="Setting category")
    is_sensitive: bool = Field(default=False, description="Whether setting contains sensitive data")
    last_updated: Optional[datetime] = Field(default=None, description="Last update timestamp")
    updated_by: Optional[str] = Field(default=None, description="User who last updated")
