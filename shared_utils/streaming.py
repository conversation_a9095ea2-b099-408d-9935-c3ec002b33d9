"""
Streaming utilities for ADK Analyst agents.
Provides streaming configuration and utilities for real-time token responses.
"""

import os
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from dataclasses import dataclass

# Use standard logging to avoid circular imports
import logging
logger = logging.getLogger(__name__)


@dataclass
class StreamingConfig:
    """Configuration for streaming responses."""
    enabled: bool = True
    delay_ms: int = 50
    buffer_size: int = 1024
    chunk_size: int = 256
    timeout_seconds: int = 30
    
    @classmethod
    def from_env(cls) -> 'StreamingConfig':
        """Create streaming config from environment variables."""
        return cls(
            enabled=os.getenv('ENABLE_STREAMING', 'true').lower() == 'true',
            delay_ms=int(os.getenv('STREAM_DELAY_MS', '50')),
            buffer_size=int(os.getenv('STREAM_BUFFER_SIZE', '1024')),
            chunk_size=int(os.getenv('STREAM_CHUNK_SIZE', '256')),
            timeout_seconds=int(os.getenv('STREAM_TIMEOUT_SECONDS', '30'))
        )


def get_streaming_config() -> StreamingConfig:
    """Get the global streaming configuration."""
    return StreamingConfig.from_env()


async def stream_response_chunks(
    response_text: str,
    config: Optional[StreamingConfig] = None
) -> AsyncGenerator[str, None]:
    """
    Stream response text in chunks with configurable delay.
    
    Args:
        response_text: The full response text to stream
        config: Streaming configuration (uses default if None)
        
    Yields:
        Chunks of the response text
    """
    if config is None:
        config = get_streaming_config()
    
    if not config.enabled:
        # If streaming is disabled, yield the entire response at once
        yield response_text
        return
    
    logger.debug(
        "Starting response streaming",
        extra={
            'response_length': len(response_text),
            'chunk_size': config.chunk_size,
            'delay_ms': config.delay_ms
        }
    )
    
    # Split response into chunks
    for i in range(0, len(response_text), config.chunk_size):
        chunk = response_text[i:i + config.chunk_size]
        yield chunk
        
        # Add delay between chunks for better UX
        if config.delay_ms > 0:
            await asyncio.sleep(config.delay_ms / 1000.0)
    
    logger.debug("Response streaming completed")


async def stream_json_response(
    response_data: Dict[str, Any],
    config: Optional[StreamingConfig] = None
) -> AsyncGenerator[str, None]:
    """
    Stream a JSON response with proper formatting.
    
    Args:
        response_data: The response data to stream
        config: Streaming configuration (uses default if None)
        
    Yields:
        Formatted JSON chunks
    """
    import json
    
    if config is None:
        config = get_streaming_config()
    
    # Convert to formatted JSON
    json_text = json.dumps(response_data, indent=2, ensure_ascii=False)
    
    # Stream the JSON text
    async for chunk in stream_response_chunks(json_text, config):
        yield chunk


def create_streaming_agent_config() -> Dict[str, Any]:
    """
    Create agent configuration with streaming enabled.
    
    Returns:
        Dictionary with streaming configuration for ADK agents
    """
    config = get_streaming_config()
    
    return {
        'streaming': config.enabled,
        'stream_config': {
            'delay_ms': config.delay_ms,
            'buffer_size': config.buffer_size,
            'chunk_size': config.chunk_size,
            'timeout_seconds': config.timeout_seconds
        }
    }


def get_model_streaming_config() -> Dict[str, Any]:
    """
    Get model-specific streaming configuration.
    
    Returns:
        Dictionary with model streaming settings
    """
    config = get_streaming_config()
    
    return {
        'streaming': config.enabled,
        'stream_options': {
            'include_usage': True,
            'include_metadata': True
        }
    }


async def validate_streaming_support() -> bool:
    """
    Validate that the current ADK version supports streaming.
    
    Returns:
        True if streaming is supported, False otherwise
    """
    try:
        import google.adk
        
        # Check ADK version
        version = getattr(google.adk, '__version__', '0.0.0')
        major, minor, patch = map(int, version.split('.'))
        
        # Streaming requires ADK 1.2.0+
        streaming_supported = (major > 1) or (major == 1 and minor >= 2)
        
        logger.info(
            "Streaming support validation",
            extra={
                'adk_version': version,
                'streaming_supported': streaming_supported
            }
        )
        
        return streaming_supported
        
    except Exception as e:
        logger.error(f"Failed to validate streaming support: {e}")
        return False


async def setup_streaming_for_agent(agent_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Setup streaming configuration for an ADK agent.
    
    Args:
        agent_config: Existing agent configuration
        
    Returns:
        Updated agent configuration with streaming settings
    """
    streaming_supported = await validate_streaming_support()
    
    if not streaming_supported:
        logger.warning("Streaming not supported in current ADK version")
        return agent_config
    
    config = get_streaming_config()
    
    # Update agent configuration
    updated_config = agent_config.copy()
    updated_config.update({
        'streaming': config.enabled,
        'stream_delay_ms': config.delay_ms,
        'stream_buffer_size': config.buffer_size
    })
    
    # Update models configuration
    if 'models' in updated_config:
        for model in updated_config['models']:
            model['streaming'] = config.enabled
    
    logger.info(
        "Streaming configuration applied to agent",
        extra={
            'streaming_enabled': config.enabled,
            'delay_ms': config.delay_ms
        }
    )
    
    return updated_config


class StreamingMetrics:
    """Metrics collection for streaming responses."""
    
    def __init__(self):
        self.total_streams = 0
        self.total_chunks = 0
        self.total_bytes = 0
        self.average_delay_ms = 0.0
    
    def record_stream(self, chunks: int, bytes_sent: int, delay_ms: float):
        """Record metrics for a streaming response."""
        self.total_streams += 1
        self.total_chunks += chunks
        self.total_bytes += bytes_sent
        self.average_delay_ms = (
            (self.average_delay_ms * (self.total_streams - 1) + delay_ms) / 
            self.total_streams
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current streaming metrics."""
        return {
            'total_streams': self.total_streams,
            'total_chunks': self.total_chunks,
            'total_bytes': self.total_bytes,
            'average_delay_ms': self.average_delay_ms,
            'average_chunks_per_stream': (
                self.total_chunks / self.total_streams if self.total_streams > 0 else 0
            ),
            'average_bytes_per_stream': (
                self.total_bytes / self.total_streams if self.total_streams > 0 else 0
            )
        }


# Global streaming metrics instance
_streaming_metrics = StreamingMetrics()


def get_streaming_metrics() -> StreamingMetrics:
    """Get the global streaming metrics instance."""
    return _streaming_metrics


async def test_streaming_configuration():
    """Test streaming configuration and functionality."""
    logger.info("Testing streaming configuration...")
    
    # Test streaming support
    streaming_supported = await validate_streaming_support()
    print(f"✅ Streaming supported: {streaming_supported}")
    
    # Test configuration
    config = get_streaming_config()
    print(f"✅ Streaming enabled: {config.enabled}")
    print(f"✅ Delay: {config.delay_ms}ms")
    print(f"✅ Buffer size: {config.buffer_size} bytes")
    print(f"✅ Chunk size: {config.chunk_size} bytes")
    
    # Test streaming a sample response
    sample_text = "This is a test streaming response with multiple chunks to demonstrate the streaming functionality."
    
    print("\n🔄 Testing streaming chunks:")
    chunk_count = 0
    async for chunk in stream_response_chunks(sample_text, config):
        chunk_count += 1
        print(f"Chunk {chunk_count}: {len(chunk)} chars")
    
    print(f"\n🎉 Streaming test completed with {chunk_count} chunks")


if __name__ == "__main__":
    asyncio.run(test_streaming_configuration())
