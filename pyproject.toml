[tool.poetry]
name = "adk-analyst-agents"
version = "0.1.0"
description = "Enterprise-grade multi-agent system for DevOps analytics built using Google's Agent Development Kit (ADK)"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [
    {include = "agents"},
    {include = "platform"}
]

[tool.poetry.dependencies]
python = "^3.11"
google-adk = "1.2.1"
# Jenkins agent dependencies
python-jenkins = "1.8.2"
multi-key-dict = "2.0.3"
# GitHub agent dependencies
pygithub = "2.6.1"
aiohttp = "3.12.9"
pynacl = "1.5.0"
pyjwt = "2.10.1"
deprecated = "1.2.18"
# Shared dependencies
google-cloud-secret-manager = "2.23.3"
google-cloud-iam = "2.19.0"
google-cloud-logging = "3.12.1"
google-cloud-core = "2.4.3"
google-cloud-storage = "2.19.0"
google-cloud-aiplatform = "1.95.1"
pydantic = "2.11.5"
pydantic-settings = "2.9.1"
pydantic-core = "2.33.2"
httpx = "0.28.1"
tenacity = "8.5.0"
structlog = "23.3.0"
asyncio-throttle = "1.0.2"
xmltodict = "0.13.0"
# Authentication and Security
authlib = "1.6.0"
cryptography = "45.0.3"
# Web Framework
fastapi = "0.115.12"
uvicorn = "0.34.3"
# Utilities
click = "8.2.1"
python-dotenv = "1.1.0"
pyyaml = "6.0.2"
typing-extensions = "4.14.0"

[tool.poetry.group.dev.dependencies]
pytest = "8.4.0"
pytest-asyncio = "1.0.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.11.1"
black = "25.1.0"
isort = "6.0.1"
flake8 = "^6.0.0"
mypy = "1.16.0"
mypy-extensions = "1.1.0"
bandit = "^1.7.5"
pre-commit = "^3.3.3"

[tool.poetry.group.test.dependencies]
responses = "^0.23.3"
factory-boy = "^3.3.0"
freezegun = "^1.2.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["jenkins_agent", "github_agent", "shared_utils"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "jenkins.*",
    "github.*",
    "google.adk.*",
    "google.cloud.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "security: Security tests",
    "slow: Slow running tests",
]

[tool.coverage.run]
source = ["jenkins_agent", "github_agent", "shared_utils"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "test_*"]
skips = ["B101", "B601"]
