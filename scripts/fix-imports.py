#!/usr/bin/env python3
"""
Fix imports script for the new multi-agent structure.
This script updates all import statements to use the new structure.
"""

import os
import re
from pathlib import Path


def fix_imports_in_file(file_path):
    """Fix imports in a single file."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content

        # Fix shared_utils imports
        content = re.sub(
            r"from shared_utils\.([a-zA-Z_]+)",
            r"from adk_platform.shared_utils.\1",
            content,
        )

        # Fix platform.shared_utils imports (from previous fix)
        content = re.sub(
            r"from platform\.shared_utils\.([a-zA-Z_]+)",
            r"from adk_platform.shared_utils.\1",
            content,
        )

        # Fix auth imports
        content = re.sub(
            r"from auth\.([a-zA-Z_]+)", r"from adk_platform.auth.\1", content
        )

        # Fix platform.auth imports (from previous fix)
        content = re.sub(
            r"from platform\.auth\.([a-zA-Z_]+)", r"from adk_platform.auth.\1", content
        )

        # Fix middleware imports
        content = re.sub(
            r"from middleware\.([a-zA-Z_]+)",
            r"from adk_platform.middleware.\1",
            content,
        )

        # Fix platform.middleware imports (from previous fix)
        content = re.sub(
            r"from platform\.middleware\.([a-zA-Z_]+)",
            r"from adk_platform.middleware.\1",
            content,
        )

        # Fix validation imports
        content = re.sub(
            r"from validation\.([a-zA-Z_]+)",
            r"from adk_platform.validation.\1",
            content,
        )

        # Fix platform.validation imports (from previous fix)
        content = re.sub(
            r"from platform\.validation\.([a-zA-Z_]+)",
            r"from adk_platform.validation.\1",
            content,
        )

        # Fix jenkins_agent imports
        content = re.sub(
            r"from jenkins_agent\.([a-zA-Z_\.]+)", r"from agents.jenkins.\1", content
        )

        # Fix github_agent imports
        content = re.sub(
            r"from github_agent\.([a-zA-Z_\.]+)", r"from agents.github.\1", content
        )

        # Fix import statements (without from)
        content = re.sub(
            r"import shared_utils\.([a-zA-Z_\.]+)",
            r"import adk_platform.shared_utils.\1",
            content,
        )

        content = re.sub(
            r"import platform\.shared_utils\.([a-zA-Z_\.]+)",
            r"import adk_platform.shared_utils.\1",
            content,
        )

        content = re.sub(
            r"import auth\.([a-zA-Z_\.]+)", r"import adk_platform.auth.\1", content
        )

        content = re.sub(
            r"import platform\.auth\.([a-zA-Z_\.]+)",
            r"import adk_platform.auth.\1",
            content,
        )

        content = re.sub(
            r"import middleware\.([a-zA-Z_\.]+)",
            r"import adk_platform.middleware.\1",
            content,
        )

        content = re.sub(
            r"import platform\.middleware\.([a-zA-Z_\.]+)",
            r"import adk_platform.middleware.\1",
            content,
        )

        content = re.sub(
            r"import validation\.([a-zA-Z_\.]+)",
            r"import adk_platform.validation.\1",
            content,
        )

        content = re.sub(
            r"import platform\.validation\.([a-zA-Z_\.]+)",
            r"import adk_platform.validation.\1",
            content,
        )

        content = re.sub(
            r"import jenkins_agent\.([a-zA-Z_\.]+)",
            r"import agents.jenkins.\1",
            content,
        )

        content = re.sub(
            r"import github_agent\.([a-zA-Z_\.]+)", r"import agents.github.\1", content
        )

        # Only write if content changed
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ Fixed imports in: {file_path}")
            return True
        else:
            print(f"⏭️  No changes needed: {file_path}")
            return False

    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False


def main():
    """Fix imports in all Python files."""
    print("🔧 Fixing imports for new multi-agent structure...")
    print("=" * 60)

    project_root = Path(__file__).parent.parent

    # Directories to process
    directories = ["agents", "adk_platform", "tests", "scripts"]

    total_files = 0
    fixed_files = 0

    for directory in directories:
        dir_path = project_root / directory
        if not dir_path.exists():
            print(f"⚠️  Directory not found: {directory}")
            continue

        print(f"\n📁 Processing directory: {directory}")
        print("-" * 40)

        # Find all Python files
        python_files = list(dir_path.rglob("*.py"))

        for file_path in python_files:
            # Skip __pycache__ directories
            if "__pycache__" in str(file_path):
                continue

            total_files += 1
            if fix_imports_in_file(file_path):
                fixed_files += 1

    print("\n" + "=" * 60)
    print("📊 Import Fix Summary")
    print("=" * 60)
    print(f"Total files processed: {total_files}")
    print(f"Files with fixes: {fixed_files}")
    print(f"Files unchanged: {total_files - fixed_files}")

    if fixed_files > 0:
        print(f"\n✅ Successfully fixed imports in {fixed_files} files!")
        print("\nNext steps:")
        print("1. Test the imports: python scripts/test-new-structure.py")
        print("2. Test ADK web: python -m google.adk.cli web --port 8010")
        print("3. Run tests: pytest tests/")
    else:
        print("\n✅ All imports are already correct!")


if __name__ == "__main__":
    main()
