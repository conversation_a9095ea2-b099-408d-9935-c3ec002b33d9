#!/usr/bin/env python3
"""
Test script for the new multi-agent structure.
This script validates that the new structure works correctly.
"""

import sys
import os
import importlib
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all imports work correctly."""
    print("🧪 Testing imports...")

    # Test basic directory imports first
    basic_tests = [
        ("agents", None),
        ("adk_platform", None),
    ]

    print("Testing basic module access...")
    for module_name, _ in basic_tests:
        try:
            module = importlib.import_module(module_name)
            print(f"✅ {module_name} module accessible")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            return False

    # Test specific imports
    tests = [
        # Platform imports
        ("adk_platform.shared_utils.logging", "get_logger"),
        ("adk_platform.shared_utils.auth", "get_user_identity_from_context"),

        # Agent imports
        ("agents.jenkins.config.settings", "get_settings"),
        ("agents.github.config.settings", "get_settings"),
    ]
    
    results = []
    for module_name, attr_name in tests:
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, attr_name):
                print(f"✅ {module_name}.{attr_name}")
                results.append(True)
            else:
                print(f"⚠️  {module_name} imported but {attr_name} not found")
                results.append(False)
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            results.append(False)
    
    return all(results)

def test_directory_structure():
    """Test that the directory structure is correct."""
    print("\n📁 Testing directory structure...")
    
    required_dirs = [
        "agents",
        "agents/jenkins",
        "agents/github", 
        "agents/shared",
        "adk_platform",
        "adk_platform/shared_utils",
        "adk_platform/auth",
        "adk_platform/middleware",
        "adk_platform/validation",
        "deployment",
        "deployment/docker",
        "deployment/gcp",
        "deployment/github-actions",
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if full_path.exists():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ (missing)")
            all_exist = False
    
    return all_exist

def test_required_files():
    """Test that required files exist."""
    print("\n📄 Testing required files...")
    
    required_files = [
        "adk.yaml",
        "agents/jenkins/agent.py",
        "agents/jenkins/adk.yaml",
        "agents/github/agent.py", 
        "agents/github/adk.yaml",
        "adk_platform/shared_utils/__init__.py",
        "deployment/docker/Dockerfile.multi-agent",
        "deployment/docker/Dockerfile.jenkins",
        "deployment/docker/Dockerfile.github",
        "deployment/github-actions/ci-cd.yml",
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def test_adk_config():
    """Test that ADK configuration is valid."""
    print("\n⚙️  Testing ADK configuration...")
    
    try:
        import yaml
        
        adk_config_path = project_root / "adk.yaml"
        if not adk_config_path.exists():
            print("❌ adk.yaml not found")
            return False
        
        with open(adk_config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check agents configuration
        if 'agents' not in config:
            print("❌ No 'agents' section in adk.yaml")
            return False
        
        agents = config['agents']
        expected_agents = ['jenkins_agent', 'github_agent']
        
        for agent_config in agents:
            agent_name = agent_config.get('name')
            if agent_name in expected_agents:
                print(f"✅ Agent configured: {agent_name}")
                
                # Check module path
                module = agent_config.get('module')
                if module and module.startswith('agents.'):
                    print(f"✅ Module path updated: {module}")
                else:
                    print(f"⚠️  Module path may need updating: {module}")
            else:
                print(f"⚠️  Unexpected agent: {agent_name}")
        
        return True
        
    except ImportError:
        print("⚠️  PyYAML not installed, skipping YAML validation")
        return True
    except Exception as e:
        print(f"❌ Error reading adk.yaml: {e}")
        return False

def test_docker_configs():
    """Test that Docker configurations are valid."""
    print("\n🐳 Testing Docker configurations...")
    
    docker_files = [
        "deployment/docker/Dockerfile.multi-agent",
        "deployment/docker/Dockerfile.jenkins", 
        "deployment/docker/Dockerfile.github",
    ]
    
    all_valid = True
    for docker_file in docker_files:
        full_path = project_root / docker_file
        if full_path.exists():
            try:
                with open(full_path, 'r') as f:
                    content = f.read()
                
                # Basic validation
                if 'FROM python:3.12-slim' in content:
                    print(f"✅ {docker_file}")
                else:
                    print(f"⚠️  {docker_file} (unexpected base image)")
                    
            except Exception as e:
                print(f"❌ {docker_file}: {e}")
                all_valid = False
        else:
            print(f"❌ {docker_file} (missing)")
            all_valid = False
    
    return all_valid

def main():
    """Run all tests."""
    print("🚀 Testing New Multi-Agent Structure")
    print("=" * 50)
    
    tests = [
        ("Directory Structure", test_directory_structure),
        ("Required Files", test_required_files),
        ("ADK Configuration", test_adk_config),
        ("Docker Configurations", test_docker_configs),
        ("Python Imports", test_imports),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{status} {test_name}")
    
    overall_success = all(results)
    print(f"\n🎯 Overall Result: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
    
    if overall_success:
        print("\n🎉 New structure is working correctly!")
        print("\nNext steps:")
        print("1. Test ADK web interface: python -m google.adk.cli web --port 8010")
        print("2. Run agent tests: pytest tests/")
        print("3. Build Docker images: docker build -f deployment/docker/Dockerfile.multi-agent .")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
        print("You may need to run the migration script: ./scripts/migrate-to-new-structure.sh")
    
    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
