#!/bin/bash

# Setup GitHub Actions Deployment for ADK Analyst
# This script sets up the necessary Google Cloud resources and GitHub secrets
# for automated deployment to Cloud Run

set -e

# Configuration
PROJECT_ID="truxtsaas"
REGION="us-central1"
GITHUB_SA_NAME="adk-analyst-github-actions"
SERVICE_SA_NAME="adk-analyst-service"
GITHUB_ORG="truxt-ai"
GITHUB_REPO="adk-analyst"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

check_prerequisites() {
    log_step "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if gh CLI is installed (optional)
    if ! command -v gh &> /dev/null; then
        log_warning "GitHub CLI (gh) is not installed. You'll need to set secrets manually."
    fi
    
    # Check if authenticated to gcloud
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated to gcloud. Please run 'gcloud auth login'"
        exit 1
    fi
    
    # Set project
    gcloud config set project $PROJECT_ID
    log_success "Prerequisites checked"
}

create_github_service_account() {
    log_step "Creating GitHub Actions service account..."
    
    # Create service account
    if gcloud iam service-accounts describe "${GITHUB_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" &>/dev/null; then
        log_warning "Service account ${GITHUB_SA_NAME} already exists"
    else
        gcloud iam service-accounts create $GITHUB_SA_NAME \
            --display-name="ADK Analyst GitHub Actions" \
            --description="Service account for GitHub Actions deployments"
        log_success "Created service account: ${GITHUB_SA_NAME}"
    fi
    
    # Grant permissions
    log_info "Granting permissions to GitHub Actions service account..."
    
    local roles=(
        "roles/run.admin"
        "roles/storage.admin"
        "roles/iam.serviceAccountUser"
        "roles/containerregistry.ServiceAgent"
    )
    
    for role in "${roles[@]}"; do
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:${GITHUB_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
            --role="$role" \
            --quiet
        log_success "Granted role: $role"
    done
}

create_service_account() {
    log_step "Creating Cloud Run service account..."
    
    # Create service account
    if gcloud iam service-accounts describe "${SERVICE_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" &>/dev/null; then
        log_warning "Service account ${SERVICE_SA_NAME} already exists"
    else
        gcloud iam service-accounts create $SERVICE_SA_NAME \
            --display-name="ADK Analyst Service Account" \
            --description="Service account for ADK Analyst Cloud Run service"
        log_success "Created service account: ${SERVICE_SA_NAME}"
    fi
    
    # Grant permissions
    log_info "Granting permissions to service account..."
    
    local roles=(
        "roles/secretmanager.secretAccessor"
        "roles/aiplatform.user"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/cloudtrace.agent"
    )
    
    for role in "${roles[@]}"; do
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:${SERVICE_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
            --role="$role" \
            --quiet
        log_success "Granted role: $role"
    done
}

create_service_account_key() {
    log_step "Creating service account key..."
    
    local key_file="github-actions-key.json"
    
    # Remove existing key file
    if [[ -f "$key_file" ]]; then
        rm "$key_file"
    fi
    
    # Create new key
    gcloud iam service-accounts keys create "$key_file" \
        --iam-account="${GITHUB_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    log_success "Created service account key: $key_file"
    
    # Encode key for GitHub secret
    local encoded_key=$(cat "$key_file" | base64 -w 0)
    
    echo ""
    log_info "GitHub Secret Configuration:"
    echo "Secret Name: GCP_SERVICE_ACCOUNT_KEY"
    echo "Secret Value (copy this):"
    echo "----------------------------------------"
    echo "$encoded_key"
    echo "----------------------------------------"
    echo ""
    
    # Try to set secret using gh CLI
    if command -v gh &> /dev/null; then
        log_info "Setting GitHub secret using gh CLI..."
        echo "$encoded_key" | gh secret set GCP_SERVICE_ACCOUNT_KEY --repo="${GITHUB_ORG}/${GITHUB_REPO}"
        log_success "GitHub secret set successfully"
    else
        log_warning "GitHub CLI not available. Please set the secret manually:"
        echo "1. Go to https://github.com/${GITHUB_ORG}/${GITHUB_REPO}/settings/secrets/actions"
        echo "2. Click 'New repository secret'"
        echo "3. Name: GCP_SERVICE_ACCOUNT_KEY"
        echo "4. Value: (the encoded key above)"
    fi
    
    # Clean up key file
    rm "$key_file"
    log_success "Cleaned up local key file"
}

enable_apis() {
    log_step "Enabling required Google Cloud APIs..."
    
    local apis=(
        "run.googleapis.com"
        "containerregistry.googleapis.com"
        "cloudbuild.googleapis.com"
        "secretmanager.googleapis.com"
        "aiplatform.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        gcloud services enable "$api"
        log_success "Enabled API: $api"
    done
}

verify_setup() {
    log_step "Verifying setup..."
    
    # Check service accounts
    if gcloud iam service-accounts describe "${GITHUB_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" &>/dev/null; then
        log_success "GitHub Actions service account exists"
    else
        log_error "GitHub Actions service account not found"
    fi
    
    if gcloud iam service-accounts describe "${SERVICE_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" &>/dev/null; then
        log_success "Cloud Run service account exists"
    else
        log_error "Cloud Run service account not found"
    fi
    
    # Check APIs
    local required_apis=("run.googleapis.com" "containerregistry.googleapis.com")
    for api in "${required_apis[@]}"; do
        if gcloud services list --enabled --filter="name:$api" --format="value(name)" | grep -q "$api"; then
            log_success "API enabled: $api"
        else
            log_error "API not enabled: $api"
        fi
    done
}

show_next_steps() {
    log_step "Setup completed! Next steps:"
    echo ""
    echo "1. 🔐 Verify GitHub secret is set:"
    echo "   https://github.com/${GITHUB_ORG}/${GITHUB_REPO}/settings/secrets/actions"
    echo ""
    echo "2. 🚀 Test deployment by pushing to main branch:"
    echo "   git push origin main"
    echo ""
    echo "3. 📊 Monitor deployment:"
    echo "   https://github.com/${GITHUB_ORG}/${GITHUB_REPO}/actions"
    echo ""
    echo "4. 🌐 Access deployed services:"
    echo "   - Staging: Will be shown in GitHub Actions logs"
    echo "   - Production: Will be shown in GitHub Actions logs"
    echo ""
    echo "5. 📈 Monitor in Google Cloud Console:"
    echo "   https://console.cloud.google.com/run?project=${PROJECT_ID}"
    echo ""
    log_success "GitHub Actions deployment setup complete!"
}

main() {
    echo "🚀 ADK Analyst GitHub Actions Deployment Setup"
    echo "=============================================="
    echo ""
    echo "This script will set up:"
    echo "- Google Cloud service accounts"
    echo "- Required IAM permissions"
    echo "- GitHub repository secrets"
    echo "- Required Google Cloud APIs"
    echo ""
    
    read -p "Continue with setup? (y/N): " confirm
    if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
        log_info "Setup cancelled"
        exit 0
    fi
    
    check_prerequisites
    enable_apis
    create_github_service_account
    create_service_account
    create_service_account_key
    verify_setup
    show_next_steps
}

# Show usage if help requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    echo "Usage: $0"
    echo ""
    echo "This script sets up GitHub Actions deployment for ADK Analyst."
    echo "It creates the necessary Google Cloud resources and helps configure GitHub secrets."
    echo ""
    echo "Prerequisites:"
    echo "- gcloud CLI installed and authenticated"
    echo "- GitHub CLI (gh) installed (optional, for automatic secret setting)"
    echo "- Appropriate permissions in Google Cloud project"
    echo ""
    exit 0
fi

main "$@"
