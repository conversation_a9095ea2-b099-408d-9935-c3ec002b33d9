#!/bin/bash

# Migration Script: Reorganize ADK Analyst to New Multi-Agent Structure
# This script helps migrate from the old structure to the new organized structure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

show_new_structure() {
    log_step "New Project Structure"
    echo ""
    cat << 'EOF'
adk-analyst/
├── 🎯 agents/                     # Multi-Agent System
│   ├── jenkins/                   # Jenkins CI/CD Analysis Agent
│   ├── github/                    # GitHub Repository Agent
│   ├── dora_metrics/             # Future: DORA Metrics Agent
│   ├── jfrog/                    # Future: JFrog Agent
│   └── shared/                   # Agent-specific shared code
├── 🏗️ platform/                   # Platform Infrastructure
│   ├── shared_utils/             # Core utilities
│   ├── auth/                     # Authentication system
│   ├── middleware/               # FastAPI middleware
│   └── validation/               # Input validation
├── 🚀 deployment/                 # Deployment Configurations
│   ├── docker/                   # Docker configurations
│   ├── gcp/                      # Google Cloud configs
│   └── github-actions/           # CI/CD workflows
├── 📊 adk.yaml                    # Multi-agent configuration
├── 📋 requirements.txt            # Production dependencies
├── 🧪 tests/                      # Test suites
├── 📚 docs/                       # Documentation
└── 🔧 scripts/                    # Utility scripts
EOF
    echo ""
}

check_current_structure() {
    log_step "Checking current project structure..."
    
    # Check if we're in the right directory
    if [[ ! -f "adk.yaml" ]]; then
        log_error "Not in ADK Analyst project root. Please run from project root."
        exit 1
    fi
    
    # Check if migration has already been done
    if [[ -d "agents/jenkins" && -d "agents/github" && -d "platform/shared_utils" ]]; then
        log_success "New structure already exists!"
        echo ""
        log_info "Current structure:"
        tree -L 3 -I '__pycache__|*.pyc|venv|.git' . || ls -la
        echo ""
        log_warning "If you want to re-run migration, please backup and remove the new directories first."
        return 0
    fi
    
    # Check old structure
    local old_structure_exists=false
    if [[ -d "jenkins_agent" ]]; then
        log_info "Found old jenkins_agent directory"
        old_structure_exists=true
    fi
    
    if [[ -d "github_agent" ]]; then
        log_info "Found old github_agent directory"
        old_structure_exists=true
    fi
    
    if [[ -d "shared_utils" ]]; then
        log_info "Found old shared_utils directory"
        old_structure_exists=true
    fi
    
    if [[ "$old_structure_exists" == "false" ]]; then
        log_warning "Old structure not found. Migration may not be needed."
    fi
    
    return 0
}

backup_old_structure() {
    log_step "Creating backup of current structure..."
    
    local backup_dir="backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup old directories if they exist
    for dir in jenkins_agent github_agent shared_utils auth middleware validation gcp-deployment; do
        if [[ -d "$dir" ]]; then
            cp -r "$dir" "$backup_dir/"
            log_success "Backed up $dir"
        fi
    done
    
    # Backup old files
    for file in Dockerfile Dockerfile.adk docker-compose.yml docker-compose.prod.yml; do
        if [[ -f "$file" ]]; then
            cp "$file" "$backup_dir/"
            log_success "Backed up $file"
        fi
    done
    
    log_success "Backup created in: $backup_dir"
}

update_imports_in_file() {
    local file="$1"
    local temp_file="${file}.tmp"
    
    if [[ ! -f "$file" ]]; then
        return 0
    fi
    
    log_info "Updating imports in: $file"
    
    # Update imports using sed
    sed -e 's/from shared_utils\./from platform.shared_utils./g' \
        -e 's/from auth\./from platform.auth./g' \
        -e 's/from middleware\./from platform.middleware./g' \
        -e 's/from validation\./from platform.validation./g' \
        -e 's/from jenkins_agent\./from agents.jenkins./g' \
        -e 's/from github_agent\./from agents.github./g' \
        "$file" > "$temp_file"
    
    mv "$temp_file" "$file"
}

update_all_imports() {
    log_step "Updating import statements..."
    
    # Update imports in agent files
    find agents/ -name "*.py" -type f | while read -r file; do
        update_imports_in_file "$file"
    done
    
    # Update imports in platform files
    find platform/ -name "*.py" -type f | while read -r file; do
        update_imports_in_file "$file"
    done
    
    # Update imports in tests
    if [[ -d "tests" ]]; then
        find tests/ -name "*.py" -type f | while read -r file; do
            update_imports_in_file "$file"
        done
    fi
    
    # Update imports in scripts
    find scripts/ -name "*.py" -type f | while read -r file; do
        update_imports_in_file "$file"
    done
    
    log_success "Import statements updated"
}

test_new_structure() {
    log_step "Testing new structure..."
    
    # Test Python imports
    log_info "Testing Python imports..."
    
    if python -c "import agents.jenkins.agent" 2>/dev/null; then
        log_success "Jenkins agent import works"
    else
        log_warning "Jenkins agent import failed"
    fi
    
    if python -c "import agents.github.agent" 2>/dev/null; then
        log_success "GitHub agent import works"
    else
        log_warning "GitHub agent import failed"
    fi
    
    if python -c "import platform.shared_utils.logging" 2>/dev/null; then
        log_success "Platform shared_utils import works"
    else
        log_warning "Platform shared_utils import failed"
    fi
    
    # Test ADK configuration
    log_info "Testing ADK configuration..."
    if python -m google.adk.cli validate 2>/dev/null; then
        log_success "ADK configuration is valid"
    else
        log_warning "ADK configuration validation failed"
    fi
}

cleanup_old_structure() {
    log_step "Cleaning up old structure..."
    
    read -p "Do you want to remove the old directories? (y/N): " confirm
    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
        # Remove old directories
        for dir in jenkins_agent github_agent shared_utils auth middleware validation gcp-deployment; do
            if [[ -d "$dir" ]]; then
                rm -rf "$dir"
                log_success "Removed old $dir directory"
            fi
        done
        
        # Remove old files
        for file in Dockerfile Dockerfile.adk; do
            if [[ -f "$file" ]]; then
                rm "$file"
                log_success "Removed old $file"
            fi
        done
        
        log_success "Old structure cleaned up"
    else
        log_info "Old structure preserved. You can remove it manually later."
    fi
}

show_next_steps() {
    log_step "Migration completed! Next steps:"
    echo ""
    echo "1. 🧪 Test the new structure:"
    echo "   python -m google.adk.cli web --port 8010"
    echo ""
    echo "2. 🐳 Build new Docker images:"
    echo "   docker build -f deployment/docker/Dockerfile.multi-agent -t adk-analyst:multi-agent ."
    echo "   docker build -f deployment/docker/Dockerfile.jenkins -t adk-analyst:jenkins ."
    echo "   docker build -f deployment/docker/Dockerfile.github -t adk-analyst:github ."
    echo ""
    echo "3. 🚀 Deploy using new structure:"
    echo "   git add ."
    echo "   git commit -m 'Reorganize to multi-agent structure'"
    echo "   git push origin main"
    echo ""
    echo "4. 📚 Update documentation:"
    echo "   - Update README.md with new structure"
    echo "   - Update deployment guides"
    echo "   - Update development documentation"
    echo ""
    log_success "🎉 Multi-agent structure migration complete!"
}

main() {
    echo "🔄 ADK Analyst Multi-Agent Structure Migration"
    echo "=============================================="
    echo ""
    
    show_new_structure
    
    read -p "Continue with migration? (y/N): " confirm
    if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
        log_info "Migration cancelled"
        exit 0
    fi
    
    check_current_structure
    backup_old_structure
    update_all_imports
    test_new_structure
    cleanup_old_structure
    show_next_steps
}

# Show usage if help requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    echo "Usage: $0"
    echo ""
    echo "This script migrates ADK Analyst from the old structure to the new multi-agent structure."
    echo "It will:"
    echo "  1. Create backups of existing structure"
    echo "  2. Update import statements"
    echo "  3. Test the new structure"
    echo "  4. Optionally clean up old files"
    echo ""
    echo "Run this script from the ADK Analyst project root directory."
    exit 0
fi

main "$@"
