#!/bin/bash

# Test Health Endpoint for ADK Web Interface
# This script tests if the /health endpoint is available in ADK web interface

set -e

# Configuration
PORT=${1:-8010}
HOST=${2:-localhost}
HEALTH_URL="http://${HOST}:${PORT}/health"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🏥 Testing ADK Health Endpoint"
echo "=============================="
echo ""
echo "Target URL: $HEALTH_URL"
echo ""

# Test if service is running
log_info "Checking if ADK web service is running..."
if curl -s --connect-timeout 5 "http://${HOST}:${PORT}" > /dev/null 2>&1; then
    log_success "ADK web service is running on port $PORT"
else
    log_error "ADK web service is not running on port $PORT"
    echo ""
    echo "To start ADK web interface:"
    echo "  python -m google.adk.cli web --port $PORT"
    exit 1
fi

# Test health endpoint
log_info "Testing health endpoint..."
response=$(curl -s -w "%{http_code}" -o /tmp/health_response.txt "$HEALTH_URL" 2>/dev/null || echo "000")

if [[ "$response" == "200" ]]; then
    log_success "Health endpoint is available and responding"
    echo ""
    echo "Response:"
    cat /tmp/health_response.txt
    echo ""
elif [[ "$response" == "404" ]]; then
    log_warning "Health endpoint not found (404)"
    echo ""
    echo "This means ADK doesn't provide a default /health endpoint."
    echo "We may need to create a custom health endpoint or use a different approach."
    echo ""
    
    # Test root endpoint
    log_info "Testing root endpoint..."
    root_response=$(curl -s -w "%{http_code}" -o /tmp/root_response.txt "http://${HOST}:${PORT}/" 2>/dev/null || echo "000")
    
    if [[ "$root_response" == "200" ]]; then
        log_success "Root endpoint is available"
        echo "We can use the root endpoint for health checks instead."
    else
        log_warning "Root endpoint also not available (HTTP $root_response)"
    fi
    
elif [[ "$response" == "000" ]]; then
    log_error "Failed to connect to health endpoint"
else
    log_warning "Health endpoint returned HTTP $response"
    echo ""
    echo "Response:"
    cat /tmp/health_response.txt
    echo ""
fi

# Test available endpoints
log_info "Discovering available endpoints..."
echo ""

# Common endpoints to test
endpoints=("/" "/docs" "/openapi.json" "/api" "/status" "/ping")

for endpoint in "${endpoints[@]}"; do
    url="http://${HOST}:${PORT}${endpoint}"
    response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null || echo "000")
    
    if [[ "$response" == "200" ]]; then
        echo "✅ $endpoint (HTTP $response)"
    elif [[ "$response" == "404" ]]; then
        echo "❌ $endpoint (HTTP $response)"
    else
        echo "⚠️  $endpoint (HTTP $response)"
    fi
done

# Cleanup
rm -f /tmp/health_response.txt /tmp/root_response.txt

echo ""
log_info "Health endpoint test completed"
