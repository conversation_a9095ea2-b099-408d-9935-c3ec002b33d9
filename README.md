# ADK Analyst - Enterprise DevOps Analytics Platform

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Google ADK](https://img.shields.io/badge/Google%20ADK-1.2.1-green.svg)](https://github.com/google/adk)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

Enterprise-grade multi-agent system for comprehensive DevOps analytics and DORA metrics using Google's Agent Development Kit (ADK).

## 🎯 Overview

ADK Analyst is a sophisticated multi-agent platform designed for enterprise DevOps analytics, featuring specialized agents for Jenkins CI/CD analysis and GitHub repository insights. Built on Google's Agent Development Kit (ADK), it provides enterprise-grade security, performance monitoring, and comprehensive audit capabilities.

### 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    ADK Analyst Platform                     │
├─────────────────────────────────────────────────────────────┤
│  🎛️  ADK Web Interface (Port 8010)                         │
│  📊  Multi-Agent Orchestration Layer                       │
├─────────────────────────────────────────────────────────────┤
│  🔧 Jenkins Agent        │  🐙 GitHub Agent                │
│  • 6 Enterprise Tools    │  • 8 Enterprise Tools           │
│  • Real-time CI/CD Data  │  • Repository Analytics         │
│  • Build Metrics         │  • Commit Analysis              │
│  • Job Monitoring        │  • PR/Release Tracking          │
├─────────────────────────────────────────────────────────────┤
│  🛠️  Shared Utilities & Enterprise Features                │
│  • Authentication & Authorization                           │
│  • Performance Monitoring & Caching                        │
│  • Audit Logging & Security                                │
│  • Error Handling & Validation                             │
├─────────────────────────────────────────────────────────────┤
│  ☁️  Google Cloud Integration                               │
│  • ADK Framework 1.2.1                                     │
│  • Vertex AI & Gemini Models                               │
│  • Cloud Logging & Monitoring                              │
│  • Secret Manager & IAM                                    │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Key Features

### 🎯 **Multi-Agent Architecture**
- **Jenkins Agent**: 6 specialized tools for CI/CD pipeline analysis
- **GitHub Agent**: 8 comprehensive tools for repository analytics
- **Shared Utilities**: Enterprise-grade common components
- **Sequential Orchestration**: Ready for DORA metrics integration

### 🔒 **Enterprise Security**
- Read-only access patterns with comprehensive audit trails
- Google Cloud IAM integration and secret management
- Input validation and security monitoring
- Rate limiting and performance optimization

### 📊 **Real-time Analytics**
- Live Jenkins server integration (jenkins.truxt.ai)
- Real GitHub API connectivity with proper rate limiting
- Performance metrics and caching mechanisms
- Comprehensive error handling and graceful degradation

### 🎛️ **ADK Web Interface**
- Interactive web interface on port 8010
- **Real-time streaming responses** with token-by-token delivery
- Real-time agent communication and function calling
- Gemini 2.5 Pro integration for intelligent responses
- Full audit trails and session management

## 🛠️ Agent Capabilities

### 🔧 **Jenkins Agent (Production Ready)**
| Tool | Description | Status |
|------|-------------|--------|
| `test_jenkins_connection` | Validate Jenkins connectivity and credentials | ✅ |
| `get_jenkins_jobs` | Retrieve all Jenkins jobs with metadata | ✅ |
| `get_job_config` | Get detailed job configuration | ✅ |
| `get_job_builds` | Retrieve build history and metrics | ✅ |
| `get_build_artifacts` | Access build artifacts and deployments | ✅ |
| `get_jenkins_status` | Monitor Jenkins server health | ✅ |

**Real Integration**: Connected to `https://jenkins.truxt.ai/` with 31 active jobs

### 🐙 **GitHub Agent (Production Ready)**
| Tool | Description | Status |
|------|-------------|--------|
| `validate_github_connection` | Test GitHub API connectivity | ✅ |
| `get_repository_info` | Extract repository metadata and statistics | ✅ |
| `get_repository_metrics` | Calculate repository health metrics | ✅ |
| `get_commits_with_metadata` | Analyze commit history and patterns | ✅ |
| `get_commit_details` | Get detailed commit information | ✅ |
| `get_pull_requests_data` | Extract PR data and review metrics | ✅ |
| `get_releases_and_tags` | Track releases and deployment tags | ✅ |
| `get_branch_analysis` | Analyze branch patterns and protection | ✅ |

**Real Integration**: Connected to GitHub API with user `ayushkmr`

## 🏁 Quick Start

### Prerequisites
- **Python 3.11+**
- **Google Cloud Platform** account with ADK access
- **Jenkins Server** access (tested with jenkins.truxt.ai)
- **GitHub Personal Access Token**

### 🔧 Installation

1. **Clone and Setup**:
```bash
git clone <repository-url>
cd adk-analyst
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
```

2. **Install Dependencies**:
```bash
pip install -r requirements.txt
# For development
pip install -r requirements-dev.txt
```

3. **Configure Environment**:
```bash
# Jenkins Configuration
export JENKINS_URL="https://jenkins.truxt.ai/"
export JENKINS_USERNAME="admin"
export JENKINS_PASSWORD="Truxt@2025"

# GitHub Configuration  
export GITHUB_TOKEN="your-github-token"
export GITHUB_PERSONAL_ACCESS_TOKEN="your-github-token"

# Google Cloud (optional for enhanced features)
export GOOGLE_CLOUD_PROJECT="your-project-id"
```

4. **Run the Platform**:
```bash
# Start ADK Web Interface with streaming
ENABLE_STREAMING=true python -m google.adk.cli web --port 8010

# Or use individual agents via CLI
python -m google.adk.cli run jenkins_agent
python -m google.adk.cli run github_agent
```

5. **Access Web Interface**:
   - Open `http://localhost:8010`
   - Select Jenkins or GitHub agent
   - Start querying your DevOps data!

## 📁 Project Structure

```
adk-analyst/
├── 🔧 jenkins_agent/              # Jenkins CI/CD Analysis Agent
│   ├── agent.py                   # Main agent definition
│   ├── adk.yaml                   # ADK configuration
│   └── tools/                     # 6 Jenkins tools
│       ├── connection_tools.py    # Connectivity & auth
│       ├── job_tools.py          # Job management
│       ├── build_tools.py        # Build analysis
│       └── ...
├── 🐙 github_agent/              # GitHub Repository Agent  
│   ├── agent.py                   # Main agent definition
│   ├── adk.yaml                   # ADK configuration
│   └── tools/                     # 8 GitHub tools
│       ├── connection_tools.py    # GitHub API connectivity
│       ├── repository_tools.py    # Repository analytics
│       ├── commit_tools.py       # Commit analysis
│       ├── pr_tools.py           # Pull request data
│       └── ...
├── 🛠️ shared_utils/               # Enterprise Shared Components
│   ├── auth.py                    # Authentication & authorization
│   ├── logging.py                 # Structured logging
│   ├── performance.py             # Caching & optimization
│   ├── validation.py              # Input validation
│   └── security.py                # Security utilities
├── 📊 adk.yaml                    # Multi-agent configuration
├── 🐳 Dockerfile                  # Container deployment
├── 📋 requirements.txt            # Production dependencies
├── 🧪 tests/                      # Test suites
└── 📚 docs/                       # Documentation
```

## 💡 Usage Examples

### 🔧 Jenkins Analysis
```python
# Test Jenkins connectivity
from jenkins_agent.tools.connection_tools import test_jenkins_connection

result = await test_jenkins_connection(context)
print(f"✅ Jenkins: {result['data']['jenkins_url']}")
print(f"📊 Version: {result['data']['jenkins_version']}")
print(f"🔗 Status: {result['data']['connection_status']}")
```

### 🐙 GitHub Repository Analysis
```python
# Analyze repository metrics
from github_agent.tools.repository_tools import get_repository_metrics

result = await get_repository_metrics(
    repo_name="truxt-ai/black-build-agents",
    include_contributor_stats=True,
    tool_context=context
)

metrics = result['data']['metrics']
print(f"📈 Commits: {metrics['total_commits']}")
print(f"👥 Contributors: {metrics['contributors_count']}")
print(f"🌿 Branches: {metrics['branches_count']}")
```

### 🎛️ Web Interface Queries
```
User: "List all Jenkins jobs and their last build status"
Agent: [Calls get_jenkins_jobs() and provides formatted results]

User: "Show me the commit history for truxt-ai/black-build-agents"  
Agent: [Calls get_commits_with_metadata() and analyzes patterns]
```

## 🔄 DORA Metrics Integration

The platform is designed for comprehensive DORA (DevOps Research and Assessment) metrics:

### 📊 **Deployment Frequency**
- Jenkins build frequency analysis
- GitHub release tracking
- Automated deployment detection

### ⚡ **Lead Time for Changes**
- Commit-to-deployment tracking
- PR merge time analysis
- Build pipeline duration

### 🛠️ **Mean Time to Recovery**
- Incident detection and resolution
- Rollback analysis
- Recovery time tracking

### 🎯 **Change Failure Rate**
- Failed deployment detection
- Rollback frequency analysis
- Quality metrics correlation

## 🧪 Development & Testing

### 🔧 **Development Setup**
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Code formatting
black .
isort .

# Type checking
mypy jenkins_agent github_agent shared_utils

# Security scanning
bandit -r jenkins_agent github_agent shared_utils
```

### 🧪 **Testing**
```bash
# Run all tests
pytest tests/

# Run with coverage
pytest --cov=jenkins_agent --cov=github_agent --cov=shared_utils tests/

# Run specific test categories
pytest -m unit tests/
pytest -m integration tests/
```

## 🚀 Deployment

### 🐳 **Docker Deployment**
```bash
# Build image
docker build -t adk-analyst .

# Run container
docker run -p 8010:8010 \
  -e JENKINS_URL="https://jenkins.truxt.ai/" \
  -e JENKINS_USERNAME="admin" \
  -e JENKINS_PASSWORD="Truxt@2025" \
  -e GITHUB_TOKEN="your-token" \
  adk-analyst
```

### ☁️ **Google Cloud Deployment**
```bash
# Deploy to Cloud Run
./scripts/deploy.sh

# Or use the deployment configuration
gcloud run deploy adk-analyst \
  --source . \
  --platform managed \
  --region us-central1
```

## 🔒 Security & Compliance

### 🛡️ **Security Features**
- **Read-only Access**: All agents operate in read-only mode
- **Credential Management**: Secure credential storage and rotation
- **Audit Logging**: Comprehensive audit trails for all operations
- **Rate Limiting**: Intelligent rate limiting and throttling
- **Input Validation**: Comprehensive input sanitization

### 📋 **Compliance**
- **SOC 2 Ready**: Audit trails and access controls
- **GDPR Compliant**: Data privacy and protection
- **Enterprise Security**: Google Cloud security integration

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### 📝 **Development Guidelines**
- Follow PEP 8 style guidelines
- Add comprehensive tests for new features
- Update documentation for API changes
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Documentation

- 📚 **Documentation**: Check the `docs/` directory
- 🐛 **Issues**: Create an issue in the repository
- 💬 **Discussions**: Use GitHub Discussions for questions
- 📧 **Contact**: Reach out to the development team

## 🎉 Acknowledgments

- **Google ADK Team** for the excellent Agent Development Kit
- **Jenkins Community** for the robust CI/CD platform
- **GitHub** for the comprehensive API and platform
- **Contributors** who make this project possible

---

**Built with ❤️ using Google ADK 1.2.1 | Enterprise DevOps Analytics Platform**
