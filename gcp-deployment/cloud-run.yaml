apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: adk-analyst-jenkins-agent-production
  namespace: default
  labels:
    app: adk-analyst
    component: jenkins-agent
    environment: production
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
    run.googleapis.com/cpu-throttling: "false"
spec:
  template:
    metadata:
      labels:
        app: adk-analyst
        component: jenkins-agent
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "10"
        
        # Resource allocation
        run.googleapis.com/cpu: "2"
        run.googleapis.com/memory: "4Gi"
        
        # Timeout configuration
        run.googleapis.com/timeout: "3600s"
        
        # Service account for Google Cloud APIs
        run.googleapis.com/service-account: "<EMAIL>"
        
        # VPC configuration (if needed)
        # run.googleapis.com/vpc-access-connector: "projects/truxtsaas/locations/us-central1/connectors/default-connector"
        
    spec:
      containerConcurrency: 100
      timeoutSeconds: 3600
      containers:
      - name: adk-analyst
        image: ghcr.io/truxt-ai/adk-analyst:latest
        ports:
        - name: http1
          containerPort: 8000
          protocol: TCP
        
        env:
        # Google Cloud Configuration
        - name: GOOGLE_CLOUD_PROJECT
          value: "truxtsaas"
        - name: GOOGLE_CLOUD_LOCATION
          value: "us-central1"
        - name: GOOGLE_GENAI_USE_VERTEXAI
          value: "TRUE"
        
        # Jenkins Configuration
        - name: JENKINS_CREDENTIALS_SECRET
          value: "jenkins-credentials"
        - name: ALLOWED_JENKINS_DOMAINS
          value: "jenkins.truxt.ai,localhost"
        
        # Application Configuration
        - name: LOG_LEVEL
          value: "INFO"
        - name: MAX_CONCURRENT_REQUESTS
          value: "10"
        - name: RATE_LIMIT_PER_MINUTE
          value: "100"
        
        # Security Configuration
        - name: ENABLE_AUDIT_LOGGING
          value: "true"
        - name: ENABLE_RATE_LIMITING
          value: "true"
        - name: MAX_RESULTS_PER_QUERY
          value: "1000"
        
        # Performance Configuration
        - name: CONNECTION_TIMEOUT
          value: "30"
        - name: READ_TIMEOUT
          value: "60"
        - name: MAX_RETRIES
          value: "3"
        
        # Model Configuration
        - name: GEMINI_PRO_MODEL
          value: "gemini-2.5-pro-preview-05-06"
        - name: GEMINI_FLASH_MODEL
          value: "gemini-2.5-flash-preview-05-20"
        
        # Production Configuration
        - name: DEBUG
          value: "false"
        - name: TESTING
          value: "false"
        
        resources:
          limits:
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # Startup probe for slower initialization
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
  
  traffic:
  - percent: 100
    latestRevision: true
