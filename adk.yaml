# ADK Multi-Agent Configuration for DevOps Analytics
name: adk_analyst
display_name: "ADK Analyst - DevOps Analytics Platform"
description: "Enterprise-grade multi-agent system for comprehensive DevOps analytics and DORA metrics"
version: "1.0.0"

# Multi-agent configuration
agents:
  - name: "jenkins_agent"
    display_name: "Jenkins Reader Agent"
    description: "Enterprise-grade Jenkins read-only agent"
    module: "agents.jenkins.agent"
    root_agent: "root_agent"
    config_file: "agents/jenkins/adk.yaml"
    streaming: true  # Enable streaming for Jenkins agent

  - name: "github_agent"
    display_name: "GitHub Reader Agent"
    description: "Enterprise-grade GitHub read-only agent"
    module: "agents.github.agent"
    root_agent: "root_agent"
    config_file: "agents/github/adk.yaml"
    streaming: true  # Enable streaming for GitHub agent

# Global security configuration
security:
  read_only: true
  authentication_required: true
  audit_logging: true
  rate_limiting: true
  allowed_domains:
    jenkins: "${ALLOWED_JENKINS_DOMAINS:-jenkins.truxt.ai,localhost}"
    github: "api.github.com,github.com"

# Global environment configuration
environment:
  google_cloud_project: "${GOOGLE_CLOUD_PROJECT:-test-project}"
  debug: "${DEBUG:-false}"
  log_level: "${LOG_LEVEL:-INFO}"

# Web interface configuration
web:
  title: "ADK Analyst - DevOps Analytics"
  description: "Enterprise-grade multi-agent DevOps analytics platform"
  favicon: "/adk_favicon.svg"
  theme: "default"
  port: 8010
  streaming: true  # Enable streaming in web interface
  stream_delay_ms: 50  # Delay between tokens for better UX
  stream_buffer_size: 1024  # Buffer size for streaming

# Global capabilities
capabilities:
  - "Multi-agent DevOps analytics"
  - "Jenkins CI/CD analysis"
  - "GitHub repository analysis"
  - "DORA metrics calculation"
  - "Performance monitoring"
  - "Security auditing"
  - "Compliance reporting"

# Enterprise features
enterprise_features:
  - "Google Cloud IAM integration"
  - "Structured logging and monitoring"
  - "Auto-scaling and high availability"
  - "Compliance and audit trails"
  - "Performance optimization"
  - "Error handling and recovery"
  - "Multi-tenant support"

# Dependencies
dependencies:
  python: "^3.11"
  packages:
    - "google-adk"
    - "python-jenkins"
    - "pygithub"
    - "pydantic"
    - "httpx"
    - "structlog"